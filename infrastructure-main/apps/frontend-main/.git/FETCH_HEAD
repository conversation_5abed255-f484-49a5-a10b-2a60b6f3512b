3d81666a321f8975381b1cc246759419e96c7bb6		branch 'codegen-bot/update-verification-copy-free-messaging-a7f8b2' of github.com:Sababuu/frontend-main
7a6372d27b7437dcfa067f79d92064924fd1bd41	not-for-merge	branch '19-app-tour' of github.com:Sababuu/frontend-main
27f7dd07fa10a1b7e10fcf726e1d51133f54ac5f	not-for-merge	branch '2-tracking-for-google-analytics' of github.com:Sababuu/frontend-main
824e14c6fe9343e98c1cfa5af7413a01d0550e62	not-for-merge	branch '24-fix-refetching-exploring-after-logout' of github.com:Sababuu/frontend-main
ba873e973f3adf169223efb532e115e98d55b584	not-for-merge	branch '24-user-verification' of github.com:Sababuu/frontend-main
7cdf52cb2c3566eb83ec2e33be970fa42f1ef078	not-for-merge	branch '26-fix-multiple-paid-action-requests-problem' of github.com:Sababuu/frontend-main
e5c104a85aea3d28cb9227f4514f70572584bff6	not-for-merge	branch '29-implement-ui-adjustments' of github.com:Sababuu/frontend-main
3f66406f094235cd6353b74b8bd13650aa623bd6	not-for-merge	branch '36-adjust-the-process-of-receiving-likes' of github.com:Sababuu/frontend-main
1589e4b9807f75a4d611d090060513bf1926ce3c	not-for-merge	branch '37-fix-who-likes-me-action-error' of github.com:Sababuu/frontend-main
33488e2c8da4607555ab0e7adf163b522c52d8ba	not-for-merge	branch '38-change-the-loading-screen' of github.com:Sababuu/frontend-main
e8d91a26ef52172fdc56d1b1971c50cda19ff4ad	not-for-merge	branch '41-adjust-grammar-errors' of github.com:Sababuu/frontend-main
38fd8ffb8ca6501c45e103b1f1cb93346e587864	not-for-merge	branch '52-grey-out-buttons' of github.com:Sababuu/frontend-main
fef19bb8510f1763a1ed455e3864e4091106e8ac	not-for-merge	branch '54-signup-process-for-tanzania' of github.com:Sababuu/frontend-main
a4326e093e93f1ff004425f05d0c031350366ff8	not-for-merge	branch '62-fix-who-liked-me-page' of github.com:Sababuu/frontend-main
8cf28aa7c84bc67251dbbfff33f6857e04619f3a	not-for-merge	branch 'Creates-GitHub-Action-to-notify-in-Slack-when-deployment-fails' of github.com:Sababuu/frontend-main
3ab2aaff331ed117c51283ccdb1558e0b55193ca	not-for-merge	branch 'Custom-PR-ready-for-review-Slack-notification' of github.com:Sababuu/frontend-main
956ebda72e2c25d42677a258da19145a745a0890	not-for-merge	branch 'FE-hotfix-slack-notifications' of github.com:Sababuu/frontend-main
e7766af12bdabf35bc4634553f0b0bd0a2d15b25	not-for-merge	branch 'FE-refining-ready-for-review' of github.com:Sababuu/frontend-main
70ddbc297ee0f289d95104bfe6ad2cabf0c35c34	not-for-merge	branch 'FE-use-user-linear-key' of github.com:Sababuu/frontend-main
99021c97d86a2f121300bde7683fe770134ea749	not-for-merge	branch 'FE_slack_deployment_fix' of github.com:Sababuu/frontend-main
77ec666c43970de3feb0625bb85bd4ce85cb8b13	not-for-merge	branch 'bugfix/395QtS2q-40-bug-cannot-upload-profile-picture-when-registering' of github.com:Sababuu/frontend-main
d267b4ef34ec1803f372e1a385ab35078831a3b6	not-for-merge	branch 'calvin/sab-171-log-out-should-redirect-to-landing-page' of github.com:Sababuu/frontend-main
4d623033bb2adcd751a55d92e230a05c32c4ae8e	not-for-merge	branch 'calvin/sab-261-dialog-shows-most-recent-message-received-not-most-recent' of github.com:Sababuu/frontend-main
0560593f165c208ec02435f8a3bee0bf8e706532	not-for-merge	branch 'calvin/sab-277-add-placeholder-description-to-reduce-user-frustration-in' of github.com:Sababuu/frontend-main
7f88201fc93e73691c4cf751438f6a166c4ee54b	not-for-merge	branch 'calvin/sab-280-remove-modal-flickering-after-paying' of github.com:Sababuu/frontend-main
94c918b3e56717cd27f1262acf902a7c023e1dc4	not-for-merge	branch 'calvin/sab-291-dialog-box-zooms-whole-page-in-on-ios-upon-user-input-focus' of github.com:Sababuu/frontend-main
3729541b2b69f9aa6a1cc30bff764a1e208989a1	not-for-merge	branch 'calvin/sab-295-use-new-photo-uploader-to-let-user-adjust-their-individual' of github.com:Sababuu/frontend-main
b4ecbdb0f6cc53c5007f3e3ec776a853f4800b28	not-for-merge	branch 'calvin/sab-297-investigate-image-uploader-errors' of github.com:Sababuu/frontend-main
d61a538ff85004f50d6e6f996517cbb7b973e945	not-for-merge	branch 'calvin/sab-299-set-up-posthog' of github.com:Sababuu/frontend-main
e80cdd863041220afb943a0e415cb72cc99e3365	not-for-merge	branch 'calvin/sab-302-add-vercel-json-config-to-allow-reloadin-in-preview' of github.com:Sababuu/frontend-main
034e6a2577ef6f16dd883562ab514ebd11ba7576	not-for-merge	branch 'calvin/sab-308-investigate-phone-library-integration-for-firefox-opera' of github.com:Sababuu/frontend-main
34bbd341afbb42a12cc0b9c697fe172934675804	not-for-merge	branch 'calvin/sab-310-confirm-padding-on-all-modals' of github.com:Sababuu/frontend-main
3facb9a38ba6e187922854c25fb93872879426e6	not-for-merge	branch 'calvin/sab-312-possible-race-condition-between-fetchcountrydata-and-users' of github.com:Sababuu/frontend-main
2345f4d8e623e0791c233189a24dfa6ea25fb9cb	not-for-merge	branch 'calvin/sab-313-modal-for-instachat-is-bugged-on-staging-locking-the-app-and' of github.com:Sababuu/frontend-main
8391e6b82798557091d25ed9ab14366ac47b9811	not-for-merge	branch 'calvin/sab-321-make-likingpassing-async-instant' of github.com:Sababuu/frontend-main
51a113958ff95cc98e2693139ea2d214c0011c8a	not-for-merge	branch 'calvin/sab-325-zooming-in-on-ios-safari-due-to-font-size' of github.com:Sababuu/frontend-main
ac3e99ea08d1e5e288d920005ed74e0d67cb8dd8	not-for-merge	branch 'calvin/sab-330-use-cloudinary-to-handle-all-image-uploads' of github.com:Sababuu/frontend-main
901e2c0429e12c28caf3212dfe8ca3b64a7a5afc	not-for-merge	branch 'calvin/sab-337-fix-broken-messages-loading-on-tab-switch' of github.com:Sababuu/frontend-main
0c73486e64b53856bfe40522a5cab907503ecf09	not-for-merge	branch 'calvin/sab-338-bug-safari-users-unable-to-send-messages-in-splash-screen' of github.com:Sababuu/frontend-main
0a6805b5517b0b206d4667066b96aa452840ebcb	not-for-merge	branch 'calvin/sab-345-fix-initialization-of-new-dialog-due-to-missing-last-message' of github.com:Sababuu/frontend-main
122d179cdc8845483ba9d78e82fb59471768b19f	not-for-merge	branch 'calvin/sab-346-match-dialog-initialization-window-fails-to-start-message' of github.com:Sababuu/frontend-main
e1508eafabbf6534fff21ed0c5b20116dc0d59ed	not-for-merge	branch 'calvin/sab-347-implement-missing-display-mode-for-posthog-page-view-events' of github.com:Sababuu/frontend-main
7ec780e243ced8a554033e17e26de0bfff3f48b6	not-for-merge	branch 'calvin/sab-354-dont-let-user-delete-a-photo-if-they-dont-have-2-photos-in' of github.com:Sababuu/frontend-main
3727eeb0e3de1f94332d9f08cbe07934ce2a4189	not-for-merge	branch 'calvin/sab-355-change-image-slider-shows-up-when-gallery-empty-on-profile' of github.com:Sababuu/frontend-main
9a0eb49a9f6b50cf7b7addf07ec3a982c4c167a0	not-for-merge	branch 'calvin/sab-361-dont-show-a-users-own-location-on-their-own-profile' of github.com:Sababuu/frontend-main
deb1dae3d137b78719e1cddc39abf3a74328a7bf	not-for-merge	branch 'calvin/sab-362-changes-to-poor-description-great-desc' of github.com:Sababuu/frontend-main
2f8047e6525312f230c0bba9058bccc41712bbc8	not-for-merge	branch 'calvin/sab-363-redesign-discovery-flow-and-profile-detailed-view' of github.com:Sababuu/frontend-main
f540cda01fd5f2a1e02af897d5e86cbac0fb172a	not-for-merge	branch 'calvin/sab-365-add-new-properties-to-posthog' of github.com:Sababuu/frontend-main
9a369cfa8a9b872ae10e383f89ae874e3fb132a7	not-for-merge	branch 'calvin/sab-369-posthog-add-other-option-to-delete_reason-and-pass' of github.com:Sababuu/frontend-main
f576fe61d58e9b6110ae45d4813be69eff3ffb31	not-for-merge	branch 'calvin/sab-370-investigate-likes_opened-event' of github.com:Sababuu/frontend-main
5331260044f74536ca9f49464b252e37608b3924	not-for-merge	branch 'calvin/sab-371-review-number_of_profiles-property-instead-capture-empty' of github.com:Sababuu/frontend-main
28d1f3ddb349284cb7aca32af55875836643fb56	not-for-merge	branch 'calvin/sab-372-optimise-messaging-to-make-it-instant-async' of github.com:Sababuu/frontend-main
dc0fae889bb312545cf1a040d32bb32a69473a94	not-for-merge	branch 'calvin/sab-373-lose-verification-status-if-you-change-profile' of github.com:Sababuu/frontend-main
a413a403b27bc49052a2b51d709624b25c1fe798	not-for-merge	branch 'calvin/sab-376-review-new-discovery' of github.com:Sababuu/frontend-main
e8995ea3f1efe3991d192c2f267cc8ee765f9ce3	not-for-merge	branch 'calvin/sab-382-fix-style-inconsistencies-and-button-functionality-issues' of github.com:Sababuu/frontend-main
71063ed213d45c5ca1d64db7b6a4d402e221c494	not-for-merge	branch 'calvin/sab-384-add-event-for-verify-account-opened' of github.com:Sababuu/frontend-main
8d87952bb055e34a867095c4f144e6c6159120fc	not-for-merge	branch 'calvin/sab-386-add-sticky-top-bar-with-name-age-actions' of github.com:Sababuu/frontend-main
ce91030806651ed466a68d21a7354c5069ecdb3d	not-for-merge	branch 'calvin/sab-387-enforce-mime-types-for-file-uploads' of github.com:Sababuu/frontend-main
3abfa7a87054d16d8c07d35284d7b8f214fefeba	not-for-merge	branch 'calvin/sab-389-missing-loader-in-dialogs-open-guest-profile' of github.com:Sababuu/frontend-main
0e7f4ff8785754f783ef386c27057c5a7d508fc7	not-for-merge	branch 'calvin/sab-395-integrate-posthog-surveys' of github.com:Sababuu/frontend-main
8d757dd99c03f50860538aae949ff9a58f8d8121	not-for-merge	branch 'calvin/sab-403-bug-in-tutorial-during-new-discovery-process' of github.com:Sababuu/frontend-main
a977bafe0b632724d87fd03feb56995be8607d80	not-for-merge	branch 'calvin/sab-404-adjust-base-svg-icons-to-ensure-uniform-sizes' of github.com:Sababuu/frontend-main
5a66a11faf6179e766d1207db8b7754c0f6fcec3	not-for-merge	branch 'calvin/sab-406-fix-pagination-issue-on-dialogs-for-multiple-accounts' of github.com:Sababuu/frontend-main
90c292c23b713a7fd912007da39bdbe72614b705	not-for-merge	branch 'calvin/sab-408-use-svg-icons-directly-instead-of-loading-image' of github.com:Sababuu/frontend-main
358d4a724dbbe362960199081f2d0f78c42fc33b	not-for-merge	branch 'calvin/sab-410-verification-revoked-adjustments' of github.com:Sababuu/frontend-main
e840778a58f1cb90fd72c7308cb8be6e969ca2bb	not-for-merge	branch 'calvin/sab-414-set-up-cloudinary-posthog-experiment' of github.com:Sababuu/frontend-main
030ce4c8d2ef4f330edba3f429a7852d0d2f742c	not-for-merge	branch 'calvin/sab-425-investigate-weird-scrolling-behaviour-on-dialogs-empty-page' of github.com:Sababuu/frontend-main
5d45f60613b721c9cc757d1dda59c6792b9e30f6	not-for-merge	branch 'calvin/sab-428-review-why-disliked-profiles-are-still-returned-to-user' of github.com:Sababuu/frontend-main
ac5a8aa1cebb38fe2a2b4b4ea072b65b6571c120	not-for-merge	branch 'calvin/sab-433-close-experiment-ship-new-cloudinary-uploader-to-all-users' of github.com:Sababuu/frontend-main
4ced8002153ca8de24a9bbdf1a83e192991d248e	not-for-merge	branch 'calvin/sab-437-create-flow-cards-for-deeper-questions' of github.com:Sababuu/frontend-main
ceed3b2f602fd2c6ebfeb467f3c605866d5b2027	not-for-merge	branch 'calvin/sab-442-setup-new-profile-ranking-algorithm-experiment' of github.com:Sababuu/frontend-main
64ca2388a0e2423cf5515402e32099fd71fc3ad4	not-for-merge	branch 'calvin/sab-446-close-out-new-algo-experiment-and-ship-to-all-users' of github.com:Sababuu/frontend-main
6cd534c90805079034cb576d6458a507c63a488e	not-for-merge	branch 'calvin/sab-447-apply-new-discovery-experience-to-own-profile-page' of github.com:Sababuu/frontend-main
ce6e7956700ce650972ce9583b3c985e0014e251	not-for-merge	branch 'calvin/sab-465-randomize-answer-buttons-and-make-skip-harder-to-click' of github.com:Sababuu/frontend-main
91769a4f6db44a1c2b805832add92f9bc30906c5	not-for-merge	branch 'calvin/sab-467-automatically-adjust-filters-when-profiles-run-out' of github.com:Sababuu/frontend-main
f01df657e2e85a6c7f10c4042185aa94de9fd2d4	not-for-merge	branch 'calvin/sab-473-add-bug-for-missing-profileid-in-auth-complete-step' of github.com:Sababuu/frontend-main
bcb6db3f395312ebf78aef80b593ffa8d94e6313	not-for-merge	branch 'calvin/sab-476-prevent-users-from-clicking-outside-modal' of github.com:Sababuu/frontend-main
5979bf61ca851bf1700150934c5e023dcc114018	not-for-merge	branch 'calvin/sab-482-community-adjustments' of github.com:Sababuu/frontend-main
b63653cf8c76d755a4840edce492c5313ad69833	not-for-merge	branch 'calvin/sab-484-add-a-tile-to-gallery-view-that-shows-remaining-gallery' of github.com:Sababuu/frontend-main
b9f39d1001262f0e16831d4ecac76c42d8e72dea	not-for-merge	branch 'calvin/sab-485-floating-action-bar-retained-at-top-of-discovery-after' of github.com:Sababuu/frontend-main
673a9e8741e822ada8199f4bd7b5e0ef37812c9f	not-for-merge	branch 'calvin/sab-490-fix-account-deletion-functionality-for-user-selection' of github.com:Sababuu/frontend-main
c566bacd8ac306c372eaa1f80596f08943803f85	not-for-merge	branch 'calvin/sab-492-fix-freezing-issue-in-discovery-feed-during-swiping' of github.com:Sababuu/frontend-main
8b1319b2ce8b26aaae6f60c3216c79a2b7738f53	not-for-merge	branch 'calvin/sab-493-bottom-align-messaging-in-dialogs-for-better-visibility' of github.com:Sababuu/frontend-main
d250f695b27ed163349a64c53c282c458a0a9a01	not-for-merge	branch 'calvin/sab-498-fix-aggressive-ui-jump-when-images-are-loading' of github.com:Sababuu/frontend-main
6e1d39c8579a2d307896769349a51731d0c08a89	not-for-merge	branch 'calvin/sab-500-create-compressed-user-details-flow' of github.com:Sababuu/frontend-main
72a493e178eb6058e8e4628f154d1231102909e2	not-for-merge	branch 'calvin/sab-508-floating-action-bar-issue-for-users-in-discovery-view' of github.com:Sababuu/frontend-main
6af3a50e1c4e15d99917f5defb07d9ceaf941a20	not-for-merge	branch 'calvin/sab-511-add-front-end-data-validation-for-name-field-in-onboarding' of github.com:Sababuu/frontend-main
6f11392060308c002eea53570013c2a870e748a1	not-for-merge	branch 'calvin/sab-512-track-user-clicks-on-the-floating-action-bar' of github.com:Sababuu/frontend-main
7789628f89c1f3bad190737edd8bb8a9f9f43934	not-for-merge	branch 'calvin/sab-514-fix-multiple-wallet_ids-bug' of github.com:Sababuu/frontend-main
b09f21d0d9758613a000ef7dee554f3e79b49c17	not-for-merge	branch 'calvin/sab-517-adjust-take-a-chance-instachat-wait-copy' of github.com:Sababuu/frontend-main
3d1fb850f8675199e8afe74798fa2fdb8eff91fe	not-for-merge	branch 'calvin/sab-523-adjust-dialog-init-for-boosted-profiles' of github.com:Sababuu/frontend-main
deeb720fcd71abfcf67f2949b478f1d936efd846	not-for-merge	branch 'calvin/sab-534-call-heartbeat-only-for-verified-users-with-complete' of github.com:Sababuu/frontend-main
0dbfc8e68204687f4d2511a72eb4acaa0e2a49d2	not-for-merge	branch 'calvin/sab-538-make-app-specific-landing-page' of github.com:Sababuu/frontend-main
6020ffb3bd20a6351ff4273f964cc78b1caff490	not-for-merge	branch 'calvin/sab-539-update-app-to-be-kenya-aware' of github.com:Sababuu/frontend-main
35cf02cdb360f81d0b5c70033a6c73cd3a4345a8	not-for-merge	branch 'calvin/sab-541-use-cropping-library-to-streamline-upload-ux' of github.com:Sababuu/frontend-main
c363fdc47a510d94b8d8c808001a1f3b9370ddb8	not-for-merge	branch 'calvin/sab-543-create-image-blur-posthog-experiment' of github.com:Sababuu/frontend-main
ce7638853c1e4ff026d5a677bce161edb54d0aaf	not-for-merge	branch 'calvin/sab-547-adjust-placement-of-last-message-datetime-display' of github.com:Sababuu/frontend-main
4cc989c1fcc49f48e3740514ca24d6c27ce797d5	not-for-merge	branch 'calvin/sab-552-start-recording-users-on-first-step-of-onboarding-process' of github.com:Sababuu/frontend-main
5faac75a48dcc97c6996cc6cb5da35aaf3f050d9	not-for-merge	branch 'calvin/sab-555-send-new-roas-events' of github.com:Sababuu/frontend-main
fc2dd3c69f00e61065a28802e1c78e8f007cabdd	not-for-merge	branch 'calvin/sab-557-simplify-date-input-component-to-individual-yearmonthday' of github.com:Sababuu/frontend-main
b2cd1571d9556f28c9d002b8f7db99b2ad690197	not-for-merge	branch 'calvin/sab-558-improve-photo-upload-page-with-intuitive-input-boxes' of github.com:Sababuu/frontend-main
2b201e4c96951ad7c2994aca85a59ca3ca81060e	not-for-merge	branch 'calvin/sab-560-adjust-default-age-filter-based-on-onboarding-user-age' of github.com:Sababuu/frontend-main
fc788d7c7777495c1fa296376d70d6b40f03d1d8	not-for-merge	branch 'calvin/sab-565-fix-infinite-loading-on-login-for-unregistered-numbers' of github.com:Sababuu/frontend-main
683428768b46850ef5969df925b06b3b3ea5047b	not-for-merge	branch 'calvin/sab-567-standardize-profileid-naming-for-insta_chat_purchased-events' of github.com:Sababuu/frontend-main
53b2091f087dac043b225899468037ef09b6f8ce	not-for-merge	branch 'calvin/sab-568-investigate-missing-boost_bought-events-in-posthog' of github.com:Sababuu/frontend-main
15e5546088669eded939733a1ce2892ab20ddaaa	not-for-merge	branch 'calvin/sab-569-fix-who-liked-me-blur-issue-for-non-cloudinary-images' of github.com:Sababuu/frontend-main
08b55cefd984a1f6a9cb381501b085f020895a8c	not-for-merge	branch 'calvin/sab-571-adjust-landing-page-copy-call-to-action' of github.com:Sababuu/frontend-main
4600aa042751c5f90e685cfbc8392ee72df0e588	not-for-merge	branch 'calvin/sab-577-integrate-gift-sending-ui' of github.com:Sababuu/frontend-main
4a0cefa934d81ade22e0d94e9ca1eea27f21735e	not-for-merge	branch 'calvin/sab-585-setup-posthog-analytics-for-profile-v2' of github.com:Sababuu/frontend-main
19d34671f7dde2feaf0e105f8cdbda542967a20c	not-for-merge	branch 'calvin/sab-592-fix-blurred-photos-on-dialogs-page' of github.com:Sababuu/frontend-main
e0b91e690d8fa95baac7517b27d1b84fbf8b4002	not-for-merge	branch 'calvin/sab-593-onboarding-bug-fixes' of github.com:Sababuu/frontend-main
14047b2dcc442d0c05878f17815e06e461c70ee6	not-for-merge	branch 'calvin/sab-594-add-profile-reporting-to-satisfy-csae-laws' of github.com:Sababuu/frontend-main
b13d16c26bafb43f931cdba4b78a24efffc01d68	not-for-merge	branch 'calvin/sab-603-fix-boosted-profiles-requiring-payment-to-chat' of github.com:Sababuu/frontend-main
e29942d67459ce5c2f1580850b11cab02afd3644	not-for-merge	branch 'calvin/sab-624-fix-onesignal-logout-and-subscription-id-management-on' of github.com:Sababuu/frontend-main
d72535387eb43e099ee602ea4c2b4ebb2896c863	not-for-merge	branch 'calvin/sab-626-dialog-input-text-overlaps-with-gift-symbol-in-ui' of github.com:Sababuu/frontend-main
fb4770aae3672d8975e7482c34348822e2fd62a1	not-for-merge	branch 'calvin/sab-631-onboarding-redirect-issue-after-user-login' of github.com:Sababuu/frontend-main
97d91ab194e684c3ad5d55ffc10066aa086c41ab	not-for-merge	branch 'calvin/sab-638-save-state-of-the-typed-messages-in-the-dialogs' of github.com:Sababuu/frontend-main
d7fb1a795be94b59b88ad7d1119311a6f989c74a	not-for-merge	branch 'calvin/sab-649-in-app-logout-redirects-to-landing-page-instead-of-login' of github.com:Sababuu/frontend-main
ef57d621823379263244a7d6ec6286a020cd4bc4	not-for-merge	branch 'calvin/sab-652-add-verification-weight-to-algo' of github.com:Sababuu/frontend-main
b5474bf9769831a01b855b188b313b84d97dada0	not-for-merge	branch 'calvin/sab-655-refactor-top-up-page-to-display-all-three-packages-wo-having' of github.com:Sababuu/frontend-main
578a6d8559fdfa32ebab4beab39350ca8971c9e5	not-for-merge	branch 'calvin/sab-656-reduce-credit-cost-in-ke' of github.com:Sababuu/frontend-main
7458207497fb0cf41e87d6fd2587c61a61700459	not-for-merge	branch 'calvin/sab-658-setup-deep-linking-for-push-notifications' of github.com:Sababuu/frontend-main
624b5817483dcc765e9c1345e3e965242cfd1fd3	not-for-merge	branch 'calvin/sab-659-fix-broken-send-icon-on-online-now-and-match-pop-up' of github.com:Sababuu/frontend-main
722fff35591bf5eca0df014014a3543b00bf09c4	not-for-merge	branch 'calvin/sab-660-close-out-who-liked-me-blur-experiment' of github.com:Sababuu/frontend-main
b673dbabde1979dbdcb5268815e7a219c9d4731c	not-for-merge	branch 'calvin/sab-665-remove-mobile-money-verification' of github.com:Sababuu/frontend-main
0e561296d142c958d6d0466f90e87ea038eaf50d	not-for-merge	branch 'calvin/sab-674-messaging-boosted-profile-doesnt-send-reward-credits' of github.com:Sababuu/frontend-main
6f8d27de3e8f92f79ecb7409b0705873cc5b7a55	not-for-merge	branch 'calvin/sab-675-implement-back-swipe-behavior-on-android-app' of github.com:Sababuu/frontend-main
d7e1248d6a868d144ed822dd192bbc1818041c20	not-for-merge	branch 'calvin/sab-684-add-found-love-as-a-profile_delete-reason' of github.com:Sababuu/frontend-main
e25f2846d2611693586f7e84e47e1aa2b4a28462	not-for-merge	branch 'calvin/sab-696-create-intro-dialog-message-with-warning-on-scamming' of github.com:Sababuu/frontend-main
e7dceda434999ea8a1db708c391a0beed79e58e5	not-for-merge	branch 'calvin/sab-700-review-location-sharing' of github.com:Sababuu/frontend-main
70d44ae224cc8703d873a95c925f53cfe7194d24	not-for-merge	branch 'calvin/sab-701-add-top-up-prompt-for-insufficient-credits-on-boost-page' of github.com:Sababuu/frontend-main
d9917a59694302da34626cbe805668cf1477bb7c	not-for-merge	branch 'calvin/sab-702-add-context-copy-to-who-liked-me-tab' of github.com:Sababuu/frontend-main
d928b0161abe12d72278bc1c2978e48bd51e121c	not-for-merge	branch 'calvin/sab-704-fix-setting-is_boosted-on-profile-back-to-false' of github.com:Sababuu/frontend-main
60138597d0044f442bfd43c9295484126bdf3d0d	not-for-merge	branch 'calvin/sab-707-add-separate-number-request-payment-flow' of github.com:Sababuu/frontend-main
c24fb7c599f6c328ba78ebc648085ab471a939ae	not-for-merge	branch 'calvin/sab-715-implement-recipient_events-in-posthog' of github.com:Sababuu/frontend-main
f02423cfad552a02ee3be53b7e9e486916c33b06	not-for-merge	branch 'calvin/sab-719-move-tariff-enum-to-database-for-consistent-value-reference' of github.com:Sababuu/frontend-main
9c87307e653e2600ff372114a889ad2a77283940	not-for-merge	branch 'calvin/sab-720-profile-card-adjustments' of github.com:Sababuu/frontend-main
239129c4a98e34e297b3900699162885aab230a6	not-for-merge	branch 'calvin/sab-723-remove-placeholder-for-boosted_profile-instachat-message' of github.com:Sababuu/frontend-main
8ab9fae8f24afdfc56d4363f4900be4b1c9da203	not-for-merge	branch 'calvin/sab-735-fix-verification-status-updating' of github.com:Sababuu/frontend-main
ea8e8d3fd4cac01afdae2680229370f0ad4f48b6	not-for-merge	branch 'calvin/sab-737-adjust-ownprofile-page-to-update-deeper-profile-details' of github.com:Sababuu/frontend-main
d19e591157626672924149efd2f152777dddc3c7	not-for-merge	branch 'calvin/sab-742-fix-layout-break-on-mobile-on-match-splash' of github.com:Sababuu/frontend-main
faab3752624376f2d7026f19bdc82ad9fc0bebc8	not-for-merge	branch 'calvin/sab-750-apply-updates-from-test-session' of github.com:Sababuu/frontend-main
e427121006ab251882ab4195acf6a7f2d62a9489	not-for-merge	branch 'calvin/sab-752-adjust-when-we-request-for-push-permissions' of github.com:Sababuu/frontend-main
4e55cc075b855dfc1c98e529899e9d0b3e090140	not-for-merge	branch 'calvin/sab-756-initialize-onesignal-later-in-the-user-journey' of github.com:Sababuu/frontend-main
52655f77d0998eaf008869b7d9a712c15cb85101	not-for-merge	branch 'calvin/sab-782-close-out-profile-v2-new-layout-experiment' of github.com:Sababuu/frontend-main
0ac9d684155e56fe6622a68afeb9fc3b45669a8a	not-for-merge	branch 'calvin/sab-802-update-frontend-code-for-custom-liking-and-feed' of github.com:Sababuu/frontend-main
0b19ba37d8fd31265cf89822c0867b8b45c954aa	not-for-merge	branch 'calvin/sab-806-fix-no-profiles-when-first-loading-up-the-app-and-also-no' of github.com:Sababuu/frontend-main
2d081181a7eb02108dcfc3dec161f880b7ef5bda	not-for-merge	branch 'calvin/sab-813-make-title-fonts-on-profilecard-consistent-and-adjust' of github.com:Sababuu/frontend-main
121374780085aeee174d785766cf777c7c3cdd40	not-for-merge	branch 'calvin/sab-815-improve-native-app-splash-screen-to-app-load-sequence' of github.com:Sababuu/frontend-main
501b4944c7fda6fe009610ee867199cb6f7cf52d	not-for-merge	branch 'calvin/sab-818-integrate-meta-sdk-into-native-app' of github.com:Sababuu/frontend-main
96292e2dab6a9b0efd0c3c57ce2ef1209fee7eed	not-for-merge	branch 'calvin/sab-821-add-indicator-showing-that-youre-at-the-bottom-of-a-list' of github.com:Sababuu/frontend-main
4d95f94a7e115172b5269ada066b7e9d97b0ed05	not-for-merge	branch 'calvin/sab-823-add-modal-title-above-comment-input-box' of github.com:Sababuu/frontend-main
60c7d03d8e765f59c902f427e7abdec03535ae7d	not-for-merge	branch 'calvin/sab-825-add-compulsory-location-sharing-to-onboarding-flow' of github.com:Sababuu/frontend-main
bb4bb6feab629a4ed4a368d6a1fd8b82ea5d6790	not-for-merge	branch 'calvin/sab-827-fix-broken-opening-dialogs-in-native-app' of github.com:Sababuu/frontend-main
192916ffe262248968b367afb3fb0b61b21e9341	not-for-merge	branch 'calvin/sab-834-review-register-flow-update-phone' of github.com:Sababuu/frontend-main
440a7d8dbe62ab7674f61dff5ba02c7ab12620a4	not-for-merge	branch 'calvin/sab-837-add-verified-badge-for-all-profile-picture-areas' of github.com:Sababuu/frontend-main
15ec1ea0c7b5a16c1b9a16e002982993f2e1c3fc	not-for-merge	branch 'calvin/sab-838-camera-access-issue-during-account-verification-for-user' of github.com:Sababuu/frontend-main
6a41baacd5936b056559fe8ba07d6958d2957c0a	not-for-merge	branch 'calvin/sab-850-call-onboarding-location-flag-after-posthog-identify' of github.com:Sababuu/frontend-main
d35c2f771e2eba7d6bd9b5aa06c5e160ef720ff7	not-for-merge	branch 'calvin/sab-855-fix-actionable-type-reference-for-user-property' of github.com:Sababuu/frontend-main
c869a6ed4f73f3daffdb564dc0fbe96b95769fe3	not-for-merge	branch 'calvin/sab-861-adjust-profile-layout-for-more-images-variation' of github.com:Sababuu/frontend-main
4a7871d2c40238fbbbdb87f0475817f6c866e998	not-for-merge	branch 'calvin/sab-881-setup-tiktok-ads-tracking' of github.com:Sababuu/frontend-main
424f0f9b4c899bea412501c74f29eb5e90504ad7	not-for-merge	branch 'calvin/sab-883-onboarding-freezing-on-userphotos-next-click' of github.com:Sababuu/frontend-main
7cf82364dcf4294df0d7020fc316e159e85110b9	not-for-merge	branch 'calvin/sab-894-fix-gift-button-functionality-in-dialog-input-area' of github.com:Sababuu/frontend-main
5ae14727b76bf5772040af90a197e16e2ed66d71	not-for-merge	branch 'calvin/sab-895-fix-and-unify-instachat-button-in-online-now-and-discovery' of github.com:Sababuu/frontend-main
5d9054fb2aee8c6e3318ce012e196334e48f5bf9	not-for-merge	branch 'calvin/sab-907-create-new-feed-page' of github.com:Sababuu/frontend-main
cedcc06cca7c05205326d612041639c53a179a17	not-for-merge	branch 'codegen-bot/add-filter-text-to-button-1749126918' of github.com:Sababuu/frontend-main
7d049da9f69400dd767c1707ca5add465fba4f85	not-for-merge	branch 'codegen-bot/fix-alternate-payment-phone-condition-sab927' of github.com:Sababuu/frontend-main
ac79633b70cd8505ce7fe466d4a681e0ea6471bd	not-for-merge	branch 'codegen-bot/fix-boosted-profile-spacing-sab921' of github.com:Sababuu/frontend-main
7140b7d3d42431cd3b93ec354badeb0e8a6867d0	not-for-merge	branch 'codegen-bot/fix-instachat-gradient-to-solid-color' of github.com:Sababuu/frontend-main
1a509e5f6f60b0af4dd6a29b98a6474f11fdd40b	not-for-merge	branch 'codegen-bot/fix-verification-duplicates-frontend-1748537801' of github.com:Sababuu/frontend-main
2bf46e97f0f281d32efb751439db49ca6eb30b3d	not-for-merge	branch 'codegen-bot/fix-verification-update-card-answered-event-1749639745' of github.com:Sababuu/frontend-main
3968810175eee5a4a057ba1308eb07738bc8dff3	not-for-merge	branch 'codegen-bot/fix-wallet-button-styling-and-order-sab936' of github.com:Sababuu/frontend-main
a6f0e046236ab4fd0a26a092f8269c9c41374ccc	not-for-merge	branch 'codegen-bot/improve-data-preloading-sab-882' of github.com:Sababuu/frontend-main
4160d52c3f514e2f012ef681cf34e90dba2ca239	not-for-merge	branch 'codegen-bot/optimize-data-preloading-sab-882' of github.com:Sababuu/frontend-main
cdbe66f773175d277bd01ad0e53fbf0a04354c1b	not-for-merge	branch 'codegen-bot/sab-705-add-50-credit-package-and-boost-pricing' of github.com:Sababuu/frontend-main
ee1c509deab16179052215ddd92e72ef60c227e9	not-for-merge	branch 'codegen-bot/sab-887-disable-tour-logic' of github.com:Sababuu/frontend-main
6de05304d2dc740834d36fdcd2ca58a014e613ba	not-for-merge	branch 'codegen-bot/sab-893-round-avatar-corners' of github.com:Sababuu/frontend-main
210c316da26e4550582b3fed2ca8798cf99599de	not-for-merge	branch 'codegen-bot/sab-912-fix-pinia-persistence-and-null-safety' of github.com:Sababuu/frontend-main
86d77ba1852befcfd6ed65a403eb69f2d91eaf1b	not-for-merge	branch 'codegen-bot/sab-915-remove-dot-list-style' of github.com:Sababuu/frontend-main
029f3a89fdd13cc612989a82a426c20acdbc2672	not-for-merge	branch 'codegen-bot/sab-941-implement-session-duration-tracking' of github.com:Sababuu/frontend-main
1167c139ef4fd605b5c802a674828e8039eff37c	not-for-merge	branch 'codegen-bot/sab-962-remove-km-away-for-profiles-without-shared-location' of github.com:Sababuu/frontend-main
e93dc03ab7e64120dbd399df4e315d88314bef1c	not-for-merge	branch 'codegen-bot/sab-963-fix-guest-profile-spacing-1749473324' of github.com:Sababuu/frontend-main
a67bc0b4a05dde9f866f67b439e0a8d051be4abe	not-for-merge	branch 'codegen-bot/sab-965-remove-heart-icon-online-now-1749480706' of github.com:Sababuu/frontend-main
d467541dd2f4ee8af321abdfda0ce577941b3144	not-for-merge	branch 'codegen-bot/sab-967-update-gift-message-copy-and-display-format' of github.com:Sababuu/frontend-main
c8317acc65e9d07780f9330464e48e559bde03ab	not-for-merge	branch 'codegen-bot/sab-970-fix-deleted-image-who-liked-me' of github.com:Sababuu/frontend-main
5887a72887faa5be3dd4c1f9fc46f5ee703b2d70	not-for-merge	branch 'codegen-bot/sab-972-fix-verification-card-logic-1749567849' of github.com:Sababuu/frontend-main
25547f79bb38dd2cfe0b9e8297b11baa5c9b2c79	not-for-merge	branch 'codegen-bot/sab-977-add-verified-field-to-like-reveal-events-1749642973' of github.com:Sababuu/frontend-main
d1f3f11e5f0f882b4f9bbe59c030aaebe26b1bf4	not-for-merge	branch 'codegen-bot/typescript-migration' of github.com:Sababuu/frontend-main
600701e4647577b194315ae10fb47dc759539cd3	not-for-merge	branch 'codegen-bot/update-firebase-roas-values' of github.com:Sababuu/frontend-main
b7b7faa71bd1e1c5cd018276cd280e34852f96f2	not-for-merge	branch 'codegen/sab-835-make-github-workflow-that-prepares-github-release-when-we' of github.com:Sababuu/frontend-main
5c5fd44618d9b8a296bc63d05538c4f4c0de2508	not-for-merge	branch 'codegen/sab-847-improve-ux-for-credit-packages-in-instachat-flow' of github.com:Sababuu/frontend-main
b3d4b04f0f2f1e31550895ed153c7661f6245ec9	not-for-merge	branch 'codegen/sab-856-review-ux-for-gender-confirmation-during-signup-process' of github.com:Sababuu/frontend-main
c0746c9b0cc3a6f0b70d41b31b6a0b8826cd86c8	not-for-merge	branch 'codegen/sab-863-some-requests-to-properties-have-a-value-entry-of-null' of github.com:Sababuu/frontend-main
c39cd938f791658f31279400b97729cc46d54165	not-for-merge	branch 'codegen/sab-887-disable-tour-logic-entirely' of github.com:Sababuu/frontend-main
f2a01339ffa4abc1b4b810b0fb1475753b256ec6	not-for-merge	branch 'codegen/sab-917-refresh-profiles-after-requesting-for-verification-on-own' of github.com:Sababuu/frontend-main
2d50d9182ec649c878755b5007c6147c803d6b84	not-for-merge	branch 'codegen/sab-919-fix-ui-calling-notifications-endpoint-before-user' of github.com:Sababuu/frontend-main
0d27376e8ea2037be0c571b0c1bbf5ee80b664cf	not-for-merge	branch 'codegen/sab-940-disable-boost-until-ads-are-running-again' of github.com:Sababuu/frontend-main
183387c0bb55b11f16b24213f946d9bbc5bc6410	not-for-merge	branch 'codegen/sab-947-ensure-location-coords-during-onboarding' of github.com:Sababuu/frontend-main
5167131c32d9f7f480a689c6847bc867e14b4f26	not-for-merge	branch 'codegen/sab-959-improve-dialogs-and-dialog-page-cache-strategy' of github.com:Sababuu/frontend-main
48d4e212f136954925646972e0355c637f5a3f65	not-for-merge	branch 'codegen/sab-961-change-copy-from-hide-and-report-this-user-to-just-report' of github.com:Sababuu/frontend-main
06e5effddadfe5110bf79c584718389a1b717641	not-for-merge	branch 'codegen/sab-969-guest-profile-view-report-not-loading' of github.com:Sababuu/frontend-main
5a1c1fdd5557523e20c094cc21fc940790893af8	not-for-merge	branch 'codegen/sab-978-change-copy-of-network-connection-error-message' of github.com:Sababuu/frontend-main
b9d56183bd7e4c89ca0a99751c6b7d722e315fad	not-for-merge	branch 'codegen/sab-979-adjust-phone-number-formating-regex' of github.com:Sababuu/frontend-main
9a48b37bc3fc0576f35af45f1f6bbbc2b0c1411b	not-for-merge	branch 'collins/sab-134-stop-behaviour-long-pressing-unrevealed-who-liked-me-profile' of github.com:Sababuu/frontend-main
ae2c671f0f283c37e04496e984096ece2181dabe	not-for-merge	branch 'collins/sab-135-immediate-ga-tracking-scope-for-now' of github.com:Sababuu/frontend-main
5fc34d76e4bc4a33038245c81cecc125b6bf64dd	not-for-merge	branch 'collins/sab-136-geolocating-instead-of-preselect-location-filters' of github.com:Sababuu/frontend-main
9560de998e6c7ad722989a788c4011c0218430c9	not-for-merge	branch 'collins/sab-139-enable-push-notifications-in-twa' of github.com:Sababuu/frontend-main
afd687bd94260f685a4ebdc0b9d7a57f0f52459a	not-for-merge	branch 'collins/sab-150-visual-bug-continue-button-in-verification-flow' of github.com:Sababuu/frontend-main
f168af73c51feb3c4d3a3278ea7890bf9dd56c37	not-for-merge	branch 'collins/sab-161-add-explainer-for-male-profiles-sending-the-first-message-to' of github.com:Sababuu/frontend-main
d418114e1aa92301440317aa50682dfeb1a7c538	not-for-merge	branch 'collins/sab-163-update-manifest-and-metadata-for-tanzania' of github.com:Sababuu/frontend-main
24ab5cc99e0a8a6a7575340e412de21d398facad	not-for-merge	branch 'collins/sab-164-change-colour-scheme-from-red-to-orange' of github.com:Sababuu/frontend-main
803d528cf636fae5040daba6dd23164fecd5f9b4	not-for-merge	branch 'collins/sab-166-vodacom-tanzania-adjustments' of github.com:Sababuu/frontend-main
43d90a082e60311ef86240177656de969e2a9f5a	not-for-merge	branch 'collins/sab-169-add-notification-actions-handling-fe' of github.com:Sababuu/frontend-main
72be4922d93b1e96a4f8400025c7b147e66e806a	not-for-merge	branch 'collins/sab-173-implement-logrocket' of github.com:Sababuu/frontend-main
f12fe4d76c81d387f423fb57494031d3e5b92a9e	not-for-merge	branch 'collins/sab-174-improve-notification-logic-fe' of github.com:Sababuu/frontend-main
97fa835c18bb8a89d1aee40373b52ac587bf8509	not-for-merge	branch 'collins/sab-177-add-verification-nudge-to-verify-button' of github.com:Sababuu/frontend-main
1bd9f7658cf02f65c84ace665f5b8b3d5b2a68fe	not-for-merge	branch 'collins/sab-186-handle-leading-zeroes-in-signup' of github.com:Sababuu/frontend-main
eb9eab9983388dadc2f26628e9590522f2eadfce	not-for-merge	branch 'collins/sab-196-segment-tracking-updates' of github.com:Sababuu/frontend-main
3c571df6328cc7d37b81179a3723fb16ca722266	not-for-merge	branch 'collins/sab-202-frontend-changes-for-discoveryprofile-view' of github.com:Sababuu/frontend-main
50ffa07ab84a3c606d6fd3c19e6b2ae462a4e3a6	not-for-merge	branch 'collins/sab-204-fix-age-filter-to-start-at-18-our-minimum-allowed-age' of github.com:Sababuu/frontend-main
72787ecd6ec982974b9a68e1724f627cd9dafe76	not-for-merge	branch 'collins/sab-205-adjust-logrocket-to-skip-landing-page' of github.com:Sababuu/frontend-main
48ffa3a0210cca45d3c2b9cfe298aa0dc452b4ef	not-for-merge	branch 'collins/sab-225-adjust-your-filter-interactive-button-at-the-end-of' of github.com:Sababuu/frontend-main
b3ae055c34a2a5937b707eef79a3e2aba922524a	not-for-merge	branch 'collins/sab-227-update-user-onboarding-flow-to-use-geolocation' of github.com:Sababuu/frontend-main
0ff422c0eec82bbdd05386077801c427e9de9dd5	not-for-merge	branch 'collins/sab-230-investigate-double-events-firing-with-tracking' of github.com:Sababuu/frontend-main
4b3a376aae3d9a89a8696886ed39f1c47e476f5c	not-for-merge	branch 'collins/sab-230-investigate-double-events-firing-with-tracking-2' of github.com:Sababuu/frontend-main
23fcf7d32e4b86c3a8ce0932f087ed269d44d8a4	not-for-merge	branch 'collins/sab-234-add-distance-to-profiles-when-exploring' of github.com:Sababuu/frontend-main
2a7a4bec0e1bc7ae2be50ebc73fba0b35a2e0232	not-for-merge	branch 'collins/sab-236-move-logrocket-tracking-from-segment' of github.com:Sababuu/frontend-main
cad3f61e8d9f3c02cdd5b8ccc52882ce75013c7d	not-for-merge	branch 'collins/sab-237-sharpening-up-frontend' of github.com:Sababuu/frontend-main
f8c1e6ff603fd3bb1f5704c19a27c50f8c6f4d65	not-for-merge	branch 'collins/sab-239-create-vercel-deployment-setup' of github.com:Sababuu/frontend-main
f6946c3f03221e96e4594f07296ba33ba591a4e1	not-for-merge	branch 'collins/sab-241-tracking-get-utms-in-mixpanel-or-google-analytics-so-we-can' of github.com:Sababuu/frontend-main
0450f1dbbf249a2041a8307195a0bf8f2ddd2050	not-for-merge	branch 'collins/sab-247-investigate-location-permission-bugs' of github.com:Sababuu/frontend-main
b00d78af72640aa1437e24bb10d56c27c6c2bd08	not-for-merge	branch 'collins/sab-247-investigate-location-permission-bugs-2' of github.com:Sababuu/frontend-main
9445b75cc91941ba14da5f629fcbdc7ba46d51d2	not-for-merge	branch 'collins/sab-254-new-image-uploading-experience' of github.com:Sababuu/frontend-main
cbdff977e8dcf9e5707d9a8a24681c6123ab5762	not-for-merge	branch 'collins/sab-257-change-discovery-loading-image' of github.com:Sababuu/frontend-main
1be498d7e66e0d7803c7ad46739767adb4199486	not-for-merge	branch 'collins/sab-42-ui-changes-for-verification-flow' of github.com:Sababuu/frontend-main
cd8ae9c870a7de7fa1fa457f0dd32159ffb92b7e	not-for-merge	branch 'collins/sab-57-tracking-for-google-analytics' of github.com:Sababuu/frontend-main
0d30e8b51cf2d8bf4781059a8ef501f75ffe7624	not-for-merge	branch 'collins/test-enable-push-notifications-in-twa' of github.com:Sababuu/frontend-main
d2f3db2f921cce09dda7638e3575b583d4fbb1dd	not-for-merge	branch 'danil/sab-540-infinite-loading-on-who-liked-me-page-is-broken' of github.com:Sababuu/frontend-main
1c3fcd1f8ed9228019c2177a4a81cf044fed5929	not-for-merge	branch 'danil/sab-542-adjust-image-layout-to-match-online-now-tab' of github.com:Sababuu/frontend-main
584ed186a5a3c51c57c38da9afda814772ee6fb3	not-for-merge	branch 'danil/sab-548-maintain-scroll-position-when-returning-to-dialogs-page' of github.com:Sababuu/frontend-main
9d1724f0e2223fe14d284105ea69d6da5620e2f7	not-for-merge	branch 'danil/sab-562-move-instachat-below-locationdistance-in-priority' of github.com:Sababuu/frontend-main
fa766831ec2bcdbd74ef5d61c5a3bf6509636744	not-for-merge	branch 'danil/sab-564-red-dots-persist-due-to-own-message-causing-notification' of github.com:Sababuu/frontend-main
eccc09c68ce185521b90c3069e3b6153cc8c4f9b	not-for-merge	branch 'danil/sab-575-create-gifts-endpoints' of github.com:Sababuu/frontend-main
8d2070d6161fd9fad76da0bd8a50233f8195f74e	not-for-merge	branch 'danil/sab-576-integrate-gifts-receiving-ui' of github.com:Sababuu/frontend-main
df87c7b3b2f4d0f80796d0927887a897a19b5408	not-for-merge	branch 'danil/sab-601-remove-help-message-pass-or-like-in-the-contexts-where-we' of github.com:Sababuu/frontend-main
a738ff09eaac78282575d152347c94c22f8ef610	not-for-merge	branch 'danil/sab-614-update-discovery-for-profile-v2' of github.com:Sababuu/frontend-main
330e28ccc3836d509db1a58dc2bb9be95e3a337c	not-for-merge	branch 'danil/sab-616-update-dialogs-dialog-for-profile-v2' of github.com:Sababuu/frontend-main
0be07c2851413373d3ff91423d7dd5abd7926ca8	not-for-merge	branch 'danil/sab-618-update-ownprofile-guestprofile-for-profile-v2' of github.com:Sababuu/frontend-main
c9f334123381a4a50fd5d37b298ae8334b10c346	not-for-merge	branch 'danil/sab-619-try-and-find-and-use-a-whatsapp-like-library-for-handling' of github.com:Sababuu/frontend-main
9790fb4845b0ea502bbad9ab5e510b131837da04	not-for-merge	branch 'danil/sab-623-update-back-and-keep-swiping-buttons-on-match-pop-up' of github.com:Sababuu/frontend-main
7cdecf500cd6958099373df9aac36d2eb0fa2a5e	not-for-merge	branch 'danil/sab-625-adjust-gift-credit-purchase-and-ui-feedback-on-discovery' of github.com:Sababuu/frontend-main
827fbee5cfebb43415305c48c904138868e1b1cb	not-for-merge	branch 'danil/sab-628-fix-time-ago-display-for-recent-likes' of github.com:Sababuu/frontend-main
864c6f4578a871c385a49bf70cab253331a9d411	not-for-merge	branch 'danil/sab-629-make-online-now-header-sticky' of github.com:Sababuu/frontend-main
dc43bdac35ba1e58b5075c3abc8f4c8cfde48d0b	not-for-merge	branch 'danil/sab-630-investigate-dialog-page-reactivity-with-notifications-and' of github.com:Sababuu/frontend-main
766781f95b27e1feb0ab7b71f6b7cdf67bdc60fa	not-for-merge	branch 'danil/sab-635-properly-handle-rendering-of-the-new-dialogs' of github.com:Sababuu/frontend-main
7bbab90ea1b333d8ec4a880d20e3dc1ee84a159c	not-for-merge	branch 'danil/sab-636-rework-hiding-dialogs-blocking-and-deleted-users-dialogs' of github.com:Sababuu/frontend-main
59a8dd68ddc050a86c1503f141b575e3a72485e5	not-for-merge	branch 'danil/sab-645-send-gift-as-message-if-you-have-an-active-dialog-with-a' of github.com:Sababuu/frontend-main
fc58c02ac939668ed9b5a72ffd4e9fd04fc8f749	not-for-merge	branch 'danil/sab-646-while-gifting-we-should-make-it-clearer-to-the-receiver-that' of github.com:Sababuu/frontend-main
3d7f1c445f63cfdff7d4fbee23da1ae7baf47f3f	not-for-merge	branch 'danil/sab-654-add-explainer-for-online-now-section-about-chatting-costs-3' of github.com:Sababuu/frontend-main
4663455b0dbde2b01e500d1187f6dffc979f08ae	not-for-merge	branch 'danil/sab-676-message-send-error-if-exist-before-delivery-is-confirmed' of github.com:Sababuu/frontend-main
59de4c958def9b21abb7b43d87d9708a0be86517	not-for-merge	branch 'danil/sab-677-add-dates-to-dialog-messages' of github.com:Sababuu/frontend-main
9a9e14d984447b907c7adc67ca8054051140652e	not-for-merge	branch 'danil/sab-686-build-docker-images-using-github-instances' of github.com:Sababuu/frontend-main
a7d97488e005e01f6faa0a319220e006932f9296	not-for-merge	branch 'danil/sab-738-update-frontend-to-use-new-api-model' of github.com:Sababuu/frontend-main
bb2e190eba0fba222359097066683b89d81afedd	not-for-merge	branch 'danil/sab-763-hotfixes-after-regress-testing-for-profile-v2' of github.com:Sababuu/frontend-main
5d6ca8eb11597da47cc3ad67093d1186c225d143	not-for-merge	branch 'danil/sab-771-create-user-entity-only-after-phone-verification' of github.com:Sababuu/frontend-main
f382609ac2231cb2a42e99ad04896458c2764969	not-for-merge	branch 'danil/sab-772-check-gender-stats-on-posthog-after-user-id-migration' of github.com:Sababuu/frontend-main
3b1a70227f92a7d0af474788fb3f426dd3456042	not-for-merge	branch 'danil/sab-777-bug-with-account-deletion-process' of github.com:Sababuu/frontend-main
c8844e58692ea4f85d77fb54cd2055b05f219607	not-for-merge	branch 'danil/sab-797-update-chat-pre-filling-logic' of github.com:Sababuu/frontend-main
9be195579700a58ec7a1281c15ce405d4b1e4e5b	not-for-merge	branch 'danil/sab-811-like-match-splash-dialog-clicking-back-breaks' of github.com:Sababuu/frontend-main
aac4ca7679dd556feab0a65db2167dd09db7f342	not-for-merge	branch 'danil/sab-814-setup-sentry-logs-for-back-end-and-front-end' of github.com:Sababuu/frontend-main
de057cd0cdc6b621fe61ba7825cea808a3c722b2	not-for-merge	branch 'danil/sab-816-implement-free-reveal-if-dislike-exists-on-likers' of github.com:Sababuu/frontend-main
0b5dc95fede2259e7f3ff248ed979f76aa8e8601	not-for-merge	branch 'danil/sab-819-make-filters-global-to-the-app' of github.com:Sababuu/frontend-main
34238de37c49db79a70470e6fe92d6ea1260225c	not-for-merge	branch 'danil/sab-822-timestamp-text-wrapping-instead-of-one-line' of github.com:Sababuu/frontend-main
75f944302622b837f0fbdb8fa89d2435abc320b6	not-for-merge	branch 'danil/sab-824-fix-missing-green-dot-for-online-status-indicator' of github.com:Sababuu/frontend-main
2dae2c221c3fee2313e1e6b1dfdb382d430e621c	not-for-merge	branch 'danil/sab-841-integrate-new-filter-approach-to-ui' of github.com:Sababuu/frontend-main
82a5340706d0e90e016a63a0005648d3d3338d57	not-for-merge	branch 'danil/sab-843-change-heart-icon-to-new-match-text' of github.com:Sababuu/frontend-main
cc565caa838690a5033419c09e8639e5671d6b95	not-for-merge	branch 'danil/sab-858-messages-intermittently-disappearing-from-chat-list' of github.com:Sababuu/frontend-main
289ae75622bb335316fe7138b8a70217617b2c4d	not-for-merge	branch 'danil/sab-859-unable-to-dislike-after-the-reveal' of github.com:Sababuu/frontend-main
209862859ece1a23715d3d6b24ea6d26e50f8e8a	not-for-merge	branch 'danil/sab-860-notifications-not-appearing-for-nikos-new-messages' of github.com:Sababuu/frontend-main
f6b09b0d045868a15c4bd148f4d0dd767cef0e7c	not-for-merge	branch 'danil/sab-874-fix-report-user-on-guest-profile' of github.com:Sababuu/frontend-main
7a82d4f36dc6ab17193502bba8947a1530935996	not-for-merge	branch 'danil/sab-900-redesign-in-app-notifications' of github.com:Sababuu/frontend-main
fce3a60771cba09d828342239b1ecad684906709	not-for-merge	branch 'danil/sab-918-adjust-new-match-badge-visuals' of github.com:Sababuu/frontend-main
4b6db813a6fc4e801a3cd35ca1f9252ece9d832d	not-for-merge	branch 'danil/sab-964-shorten-delete-message-or-disable-truncation-on-small' of github.com:Sababuu/frontend-main
546b470d6483c5fda8a17b8cdafbb3569a3e4eb1	not-for-merge	branch 'danilrodin/sab-126-allow-for-swiping-of-photos-up-down-to-browse-a-profiles' of github.com:Sababuu/frontend-main
97c1f3d649800ce85c61401df78bed9ca445d2dd	not-for-merge	branch 'danilrodin/sab-138-use-something-like-websocket-to-display-updated-balance' of github.com:Sababuu/frontend-main
****************************************	not-for-merge	branch 'danilrodin/sab-143-store-user-filters-between-sessions' of github.com:Sababuu/frontend-main
b7de62da6356b8f74475d5014aeda0123886bc01	not-for-merge	branch 'danilrodin/sab-151-april-17-bugfixes-websockets-on-staging' of github.com:Sababuu/frontend-main
abfd54b1c0dfe69b7ad65c6a09d2f824f2b15751	not-for-merge	branch 'danilrodin/sab-153-dialogs-page-refactor-redesign' of github.com:Sababuu/frontend-main
b1e1b0236b33a8568fe2f0e5a1bf912822cfdde6	not-for-merge	branch 'danilrodin/sab-156-update-block-behavior' of github.com:Sababuu/frontend-main
15ce3c64538417e906aaa2b09d10aa17332b8bc4	not-for-merge	branch 'danilrodin/sab-167-change-verification-flow-from-gov-id-to-two-selfies' of github.com:Sababuu/frontend-main
36ca3828d9aa6ffb4cdd2ce680f75e36438dbade	not-for-merge	branch 'danilrodin/sab-168-adjustments-after-tests-in-production' of github.com:Sababuu/frontend-main
5bea221c755e44cc72045bf508650a87ed2d25bf	not-for-merge	branch 'danilrodin/sab-172-fix-dialog-loading-by-id' of github.com:Sababuu/frontend-main
451aa35aed84b5e2ea964d9ce3dcc23f5b1c7c6e	not-for-merge	branch 'danilrodin/sab-195-investigate-debounce-to-handle-network-issues' of github.com:Sababuu/frontend-main
a8e17d1d996936b6442d34acedbc9017c241fbfc	not-for-merge	branch 'danilrodin/sab-197-make-blurred-photos-clickable-in-who-liked-me-page' of github.com:Sababuu/frontend-main
c547a3af843dba330689bd5f456ce19cbef800ad	not-for-merge	branch 'danilrodin/sab-198-last-seen-3mins-ago-online-now' of github.com:Sababuu/frontend-main
584c479ebd977460c1492be77517f2ddd5086187	not-for-merge	branch 'danilrodin/sab-199-fix-cache-issues' of github.com:Sababuu/frontend-main
5d89475b40016a25a8f607de592f6a31d5146c39	not-for-merge	branch 'danilrodin/sab-200-some-users-have-removed-their-profiles-causing-a-500-error' of github.com:Sababuu/frontend-main
f6e81d6c5d9c87c2cbdc67f05a7280dcfd328fb7	not-for-merge	branch 'danilrodin/sab-203-paginate-who-liked-me-to-6' of github.com:Sababuu/frontend-main
847b70ae90727125c5ab7804ad870f8e7613776f	not-for-merge	branch 'danilrodin/sab-206-image-compression-to-optimise-load-speeds' of github.com:Sababuu/frontend-main
9c033c5101322e77acadf895a001f6b6ccfdb758	not-for-merge	branch 'danilrodin/sab-210-missed-removing-from-frontend-store-after-instachat' of github.com:Sababuu/frontend-main
1ef631e945aa4bb69a4867198219c2cb1a3885ae	not-for-merge	branch 'danilrodin/sab-211-fix-credit-cost-who-liked-me-from-2-to-1-to-reflect-frontend' of github.com:Sababuu/frontend-main
335a51bdf7e2705b396fc9e499cce5071168b834	not-for-merge	branch 'danilrodin/sab-214-its-a-match-splash-missing' of github.com:Sababuu/frontend-main
03b7f050b667fa3a4b12695b88dea3f2a67d1a48	not-for-merge	branch 'danilrodin/sab-217-rewrite-load-more-mechanism-in-who-liked-me-page' of github.com:Sababuu/frontend-main
8ab9accc890421827603824f92e4b95544c9e985	not-for-merge	branch 'danilrodin/sab-231-split-chunks-for-utility-dependancies' of github.com:Sababuu/frontend-main
04d8b09e225a4e0037232bcf7317bbf65d556218	not-for-merge	branch 'danilrodin/sab-245-its-a-match-splash-bug' of github.com:Sababuu/frontend-main
8feefae712d96fbf5782ac35f798ce1c71fced07	not-for-merge	branch 'danilrodin/sab-246-let-male-user-click-on-profile-in-dialog-tab-without-paying' of github.com:Sababuu/frontend-main
900c231651b5d00e91ee98a5602bdcb3221edd5c	not-for-merge	branch 'danilrodin/sab-250-infrastructure-improvements' of github.com:Sababuu/frontend-main
35e81124fe1a3eec0eb0231d6ba7d9235c2c8073	not-for-merge	branch 'danilrodin/sab-254-new-image-uploading-experience' of github.com:Sababuu/frontend-main
4e4fca2842c0738b994ebb284694268ab635146a	not-for-merge	branch 'danilrodin/sab-254-refactor-gallery-photos-deletion' of github.com:Sababuu/frontend-main
ad0400fa339d6120803f169e1dc293cfc8f1518f	not-for-merge	branch 'danilrodin/sab-259-add-delete-reason-to-profile-deletion' of github.com:Sababuu/frontend-main
aa5883f0943a151249fc163b8e13d4dcd4aa9224	not-for-merge	branch 'danilrodin/sab-260-going-back-in-loop-trying-to-click-back-button' of github.com:Sababuu/frontend-main
f0f9586635c02610d79d84a2ffe5eb7006d3e6b1	not-for-merge	branch 'danilrodin/sab-265-explorer-bug-when-filter-is-untouched' of github.com:Sababuu/frontend-main
65664b72a91cefb45b9e2c7aa109da4371bdabbb	not-for-merge	branch 'danilrodin/sab-272-dont-reload-discovery-page-when-a-user-is-in-session' of github.com:Sababuu/frontend-main
e983db3c1c1dbb33d41df6e72207788d007f7b77	not-for-merge	branch 'danilrodin/sab-279-if-you-block-message-has-fixed-gender' of github.com:Sababuu/frontend-main
d613db0f6f25b049e8f7de443437b851b3958c79	not-for-merge	branch 'danilrodin/sab-296-fix-repeated-location-modal' of github.com:Sababuu/frontend-main
a7c942e6cb602d96938965f014eae7eb27fa0d97	not-for-merge	branch 'danilrodin/sab-301-fix-cachesession-retained-info-when-user-logs-out' of github.com:Sababuu/frontend-main
a0b86dcf7ee489b53e3f3a5babf662ca2899ae7b	not-for-merge	branch 'danilrodin/sab-304-refactor-onboarding-process' of github.com:Sababuu/frontend-main
5323c669716702f5c15aa806fff96572839fe258	not-for-merge	branch 'danilrodin/sab-311-ui-bug-when-buying-credits-all-options-show-loading-state' of github.com:Sababuu/frontend-main
aefb4c5e3b64ddd57ca37acda41c1f76f60e068d	not-for-merge	branch 'danilrodin/sab-314-bug-bouncing-bottom-info-during-verify-account-animation-in' of github.com:Sababuu/frontend-main
cf0ee46484ae86e3e5b44a23ce171691bb073008	not-for-merge	branch 'danilrodin/sab-316-fix-subscription-call-on-every-like' of github.com:Sababuu/frontend-main
c6228ba825f446fcfc9abdf990d0de345275b1f7	not-for-merge	branch 'danilrodin/sab-318-when-user-opens-specific-dialog-change-url-to-match-new' of github.com:Sababuu/frontend-main
9e94cf2b1d090a49d5f67f70fe7cc2f5b48cb579	not-for-merge	branch 'danilrodin/sab-320-clicking-shaded-area-outside-modal-should-close-modal' of github.com:Sababuu/frontend-main
a9210d92cf9a1aab3eead7da12e2fbb33c5a5cbf	not-for-merge	branch 'danilrodin/sab-323-not-able-to-like-profile' of github.com:Sababuu/frontend-main
5041d27cb4e3ca19c816c29a1ca8e3535d260a32	not-for-merge	branch 'danilrodin/sab-339-remove-paywal-when-initiating-dialogs' of github.com:Sababuu/frontend-main
f53f4aec7cef34e0ab2c7c18d672b76dff1230fa	not-for-merge	branch 'danilrodin/sab-34-implement-report-user' of github.com:Sababuu/frontend-main
1d15dbaff87dafe559daf989f939e39a2d96319a	not-for-merge	branch 'danilrodin/sab-341-investigate-invalid-date-in-last-seen-possible-cronjob-issue' of github.com:Sababuu/frontend-main
f3a7295dae3de81f0efa168d14ad945fc360743a	not-for-merge	branch 'danilrodin/sab-348-avoid-onboarding-flow-when-changing-phone-number' of github.com:Sababuu/frontend-main
7426dc10fb27c387155e1c70b27d6c0437af3336	not-for-merge	branch 'danilrodin/sab-357-prefilled-message-in-dialog-when-coming-from-match' of github.com:Sababuu/frontend-main
a8686a8cb79a6d0ca9802b00fa149b9e680d2b22	not-for-merge	branch 'danilrodin/sab-37-its-a-match-with-profile-photos' of github.com:Sababuu/frontend-main
4431fba18d2a5c562716d44b0482055b219b2a91	not-for-merge	branch 'danilrodin/sab-37-when-its-a-match-we-display-figma-graphic-we-should-display' of github.com:Sababuu/frontend-main
8b62abe28385cbf781e3c1f6aacf20244e2b11e6	not-for-merge	branch 'danilrodin/sab-374-display-phone-number-where-code-was-sent-to' of github.com:Sababuu/frontend-main
6fc50e19e580d6872b6586f648388347344068e3	not-for-merge	branch 'danilrodin/sab-383-remove-ability-to-go-back-when-at-root-page-tab-level' of github.com:Sababuu/frontend-main
60aba44fbeaae5b00752a45b0603ac40081ce421	not-for-merge	branch 'danilrodin/sab-388-apply-new-discovery-experience-to-guest-profile-and-own' of github.com:Sababuu/frontend-main
0b1b8a5ffb7c67a01eadb06b3d6acbf10bc5d903	not-for-merge	branch 'danilrodin/sab-390-instachat-should-open-new-dialog-url' of github.com:Sababuu/frontend-main
0ebd62db2dcea66ea2dd1ffc6455b5aa50da3add	not-for-merge	branch 'danilrodin/sab-396-fix-redirection-from-authcompletestep-if-you-already' of github.com:Sababuu/frontend-main
01bd69a7872103ce8151420644bfbe0f74e4910d	not-for-merge	branch 'danilrodin/sab-407-fix-issue-with-unread-notifications-not-disappearing' of github.com:Sababuu/frontend-main
cc3cd9bae6009d7e48fb552614e9753bac9d93ae	not-for-merge	branch 'danilrodin/sab-413-ensure-users-cant-upload-duplicate-images' of github.com:Sababuu/frontend-main
e7dfdef32db558d64a95d39f6ad9f3c1bdcefbf1	not-for-merge	branch 'danilrodin/sab-413-improve-error-handling' of github.com:Sababuu/frontend-main
c0a7d3c304b09bda70b42da849aecff9218a5f41	not-for-merge	branch 'danilrodin/sab-415-its-a-match-sending-message-should-actually-send-the-message' of github.com:Sababuu/frontend-main
0e4e083410c526774a672cb1868fcb4babc4dc62	not-for-merge	branch 'danilrodin/sab-418-add-image-button-doesnt-work-on-own-profile-page' of github.com:Sababuu/frontend-main
c230885dc1a6c1d7d2e5fabc2dd79669a6d1e1a0	not-for-merge	branch 'danilrodin/sab-420-use-new-display-format-for-instachat-button' of github.com:Sababuu/frontend-main
11d625bcd9eb282c4dcc870dab0e453a62489c62	not-for-merge	branch 'danilrodin/sab-421-reduce-top-up-cost-and-increase-free-credits-on-signup' of github.com:Sababuu/frontend-main
1adf3c8572aa61f65db41f4d9bbe8e5d4c367cd8	not-for-merge	branch 'danilrodin/sab-444-fix-bug-when-messages-from-likes-and-dialogs-page' of github.com:Sababuu/frontend-main
ea9abed1aa0bb0e6dee000fabab8f0942b849b0a	not-for-merge	branch 'danilrodin/sab-452-close-out-instachat-experiment-and-ship-to-all-users' of github.com:Sababuu/frontend-main
ea0914761aea9f4da753e9da62728b52d326007e	not-for-merge	branch 'danilrodin/sab-453-close-out-paid-features-experiment-and-ship-to-users' of github.com:Sababuu/frontend-main
b5ce7d80c415a68c9ebd596ed36b8c5fbf62c11b	not-for-merge	branch 'danilrodin/sab-455-integrate-online-now-endpoint-to-ui' of github.com:Sababuu/frontend-main
bc719290c216f8e29324b34dde782e548001844a	not-for-merge	branch 'danilrodin/sab-466-app-navigation-issue-returning-to-incorrect-tab-after' of github.com:Sababuu/frontend-main
53574e73ad728030a386d1ef0b983316a9448d89	not-for-merge	branch 'danilrodin/sab-479-troubleshoot-notification-issues' of github.com:Sababuu/frontend-main
795025b8df936fa208c2ec2f5484133ae17717fd	not-for-merge	branch 'danilrodin/sab-48-bug-reloading-dialog-page-opens-specific-dialog' of github.com:Sababuu/frontend-main
3e98a30ff2f3461ddcc90059f7f25fa8a06ed227	not-for-merge	branch 'danilrodin/sab-481-online-now-adjustments' of github.com:Sababuu/frontend-main
30b2fb4ada63174b60be0f75c6f38a23566f000a	not-for-merge	branch 'danilrodin/sab-60-bug-hide-menu-in-profile-after-like' of github.com:Sababuu/frontend-main
d863ae5749aa5dd6db38bb5488e622ed659d19be	not-for-merge	branch 'dependabot/npm_and_yarn/multi-2f20eee292' of github.com:Sababuu/frontend-main
c61171bab51e5d0ac6e87c74090c1b10b8cca6be	not-for-merge	branch 'deployment_successful_slack' of github.com:Sababuu/frontend-main
0d30e8b51cf2d8bf4781059a8ef501f75ffe7624	not-for-merge	branch 'detached' of github.com:Sababuu/frontend-main
2f8652a5bf3d86d22df70001453fc6099d4186b1	not-for-merge	branch 'feature/3u1f36dq-process-notifications' of github.com:Sababuu/frontend-main
975a5a5983eb1be7753420a7d816e27f74d49a42	not-for-merge	branch 'feature/8JY3NpVi-12-implement-log-out-from-the-system' of github.com:Sababuu/frontend-main
1c26450cf3d505ef3dd19eb2c1a623f0beaca109	not-for-merge	branch 'feature/HS5QYtZ4-fix-flow-of-return-to-discover-page-after-opening-profile' of github.com:Sababuu/frontend-main
f61a5d0af2338e660f2b4df12452b057824b4e1d	not-for-merge	branch 'feature/M1leQZaR-implement-pwa-transfer-to-twa' of github.com:Sababuu/frontend-main
aba7c32ce5547b921ca7d3ecfce76cd89c845ce7	not-for-merge	branch 'feature/QHRG4dJR_add-new-logo-to-new-accounts-on-prod' of github.com:Sababuu/frontend-main
e9e09359eaac1e14930099c16f9c0a48aa9746d4	not-for-merge	branch 'feature/adjust-onesignal-tags' of github.com:Sababuu/frontend-main
d186ee1a493549014b5933895d01e75f5e6cbac1	not-for-merge	branch 'feature/dKQwumk1_adding-the-ability-to-delete-a-photo-from-gallery' of github.com:Sababuu/frontend-main
c2adeb9091408f10d0ff3dab264592b71c755315	not-for-merge	branch 'feature/gATTXAgd_using-a-service-to-detect-false-explicit-photos' of github.com:Sababuu/frontend-main
19ba7af4f13946ea72481347a6d6952a997d8415	not-for-merge	branch 'feature/oTp36QYw-adding-the-ability-to-delete-a-profile' of github.com:Sababuu/frontend-main
d0bdbd20d4ef846aefed25e0af923f16289e0c5f	not-for-merge	branch 'feature/one-signal' of github.com:Sababuu/frontend-main
1264cf60fc6e6bdd59e267ee40c2cc6dc17498f4	not-for-merge	branch 'feature/tanzania-price-updates' of github.com:Sababuu/frontend-main
3f0d185c62066cc6805f9a359657752d02c124aa	not-for-merge	branch 'feature/tobey-verification-updates' of github.com:Sababuu/frontend-main
9879ccd41cd6ac123c105edfcee272a54b5bd179	not-for-merge	branch 'feature/v6GMbhL7-adding-a-badge-for-verified-users' of github.com:Sababuu/frontend-main
2807992667a3250585d0a24d1619d406f2d254fa	not-for-merge	branch 'fix-actions-in-discovery-logic' of github.com:Sababuu/frontend-main
e853215b21735a1d83279a42303eeefe161d1a68	not-for-merge	branch 'fix-an-error-on-log-out' of github.com:Sababuu/frontend-main
b0354f55092c4ba32efd59d84601b4788da8b27f	not-for-merge	branch 'fix-invalid-import' of github.com:Sababuu/frontend-main
7e292e5fef3f2759371f4b91418f54ede15690f1	not-for-merge	branch 'fix/merge-prod-to-stage' of github.com:Sababuu/frontend-main
4546dd9f972ff133b021e2295a7db2f4a120012f	not-for-merge	branch 'fixes_landing_page' of github.com:Sababuu/frontend-main
f317c1eeb4c16ad7bc06a0ba5ab1ba81f1241d61	not-for-merge	branch 'hSTpwuMd-speed-optimization-getting' of github.com:Sababuu/frontend-main
b7de62da6356b8f74475d5014aeda0123886bc01	not-for-merge	branch 'hotfix-add-event-import-for-gtag-on-auth-complete' of github.com:Sababuu/frontend-main
796c5598877ec5d517f77a51fc369224c3b32d2b	not-for-merge	branch 'hotfix/add-end-of-list-to-likes' of github.com:Sababuu/frontend-main
99abb712880a670cb25c48870d3c9a2429899b8f	not-for-merge	branch 'hotfix/add-native-route-change-handler' of github.com:Sababuu/frontend-main
cd2e537ed157ddbf33712eb89d5c12d83e364209	not-for-merge	branch 'hotfix/adjust-fingerprint-format' of github.com:Sababuu/frontend-main
ee3d60f8eb0ccacf7d2891dfb1127e655caf96c8	not-for-merge	branch 'hotfix/adjust-phone-default-and-posthog-bug' of github.com:Sababuu/frontend-main
0f1eaf2bfce70f83adba3804964bbca626087bc2	not-for-merge	branch 'hotfix/adjust-web-mounted-call' of github.com:Sababuu/frontend-main
7b9d27cb0884922ac911b5ce9179e3d5f3cd5f40	not-for-merge	branch 'hotfix/cloudinary-uploader-tweaks' of github.com:Sababuu/frontend-main
1dd8bd0230dd6ee69178cbbe02e670fc5bbcb5c5	not-for-merge	branch 'hotfix/community-copy' of github.com:Sababuu/frontend-main
1d893f1201d2c7eb1440b5d33dd47182c3e21670	not-for-merge	branch 'hotfix/credits-copy' of github.com:Sababuu/frontend-main
4c10d7b5f68ac271e57f40dd0bc2586305b583f6	not-for-merge	branch 'hotfix/description-meter-bug' of github.com:Sababuu/frontend-main
5e0eb2094501562912eaee3f07aab7f9f0bc9e95	not-for-merge	branch 'hotfix/dialogs-empty-state-prompt' of github.com:Sababuu/frontend-main
a36d12659d790a859b57cd4556fa3f322b6c1e97	not-for-merge	branch 'hotfix/disable-front-end-flow-cards' of github.com:Sababuu/frontend-main
53fa62a338871dbd21632b8e502ba701b58a2733	not-for-merge	branch 'hotfix/disable-gift-on-boost' of github.com:Sababuu/frontend-main
3f2433edef47eb4d21f20b239549905c0ad27411	not-for-merge	branch 'hotfix/fix-broken-onboarding' of github.com:Sababuu/frontend-main
c63b6482cca31ce5d1ba8ea075c2966a6a2d19b1	not-for-merge	branch 'hotfix/fix-invalid-verified-icon' of github.com:Sababuu/frontend-main
c95f80a4eb0e7782c0b5154360891d4fd2d861f1	not-for-merge	branch 'hotfix/guest-profile-loader' of github.com:Sababuu/frontend-main
5b610e30b38d4de496764a4bc4385ea2d133515f	not-for-merge	branch 'hotfix/meta-complete-registration' of github.com:Sababuu/frontend-main
6aceae131eff02f59e5469a782e7a0dc375a2d9e	not-for-merge	branch 'hotfix/more-cloudinary-tweaks' of github.com:Sababuu/frontend-main
d404e96945edaebbef07acb10e0a3636b82c31cd	not-for-merge	branch 'hotfix/onboarding-avatar-upload-etag' of github.com:Sababuu/frontend-main
952252463cadef54c985487ca7c922a61411abd4	not-for-merge	branch 'hotfix/onboarding-location-flow' of github.com:Sababuu/frontend-main
287aeb9ea945e14a19b00d6de61aaf2fd770ff23	not-for-merge	branch 'hotfix/profile-actions-events' of github.com:Sababuu/frontend-main
fc793339a53acdfbc7a81d2e775ec86e258460af	not-for-merge	branch 'hotfix/profile-verification-proper-headers' of github.com:Sababuu/frontend-main
a3fc1356f88e8cb2afab00e4279e1e1221c2a0fb	not-for-merge	branch 'hotfix/redirect-to-auth-from-landing-page' of github.com:Sababuu/frontend-main
850c3b82c81665fe7df6be5cb6be3446a2b4b2e2	not-for-merge	branch 'hotfix/slack-notifications' of github.com:Sababuu/frontend-main
395625b370f76e94ec0959002806587ce6d3c806	not-for-merge	branch 'hotfix/update-universal-link-signatures' of github.com:Sababuu/frontend-main
d8dcf040e22ff6ff80a31e788ea3ffaff513ec9e	not-for-merge	branch 'hotfix/who-liked-me-blur-bug' of github.com:Sababuu/frontend-main
9e400e090e80375c894aed84c41d6da8c4ab83fc	not-for-merge	branch 'jakob/sab-462-annotations-api-github-automation' of github.com:Sababuu/frontend-main
a039a3c8efe5bfd95a9820a71e1a3bc5f14fe7a4	not-for-merge	branch 'jakob/sab-474-fix-bugs-in-annotations-api' of github.com:Sababuu/frontend-main
2e118f70135dfa56ba4b7d9caba06dbd88f50e83	not-for-merge	branch 'jakob/sab-478-use-github-pr-description-to-populate-linear-comment-when' of github.com:Sababuu/frontend-main
1c11762ffd321ca25bbf37e46c3ccf22ccb2ddfa	not-for-merge	branch 'jakob/sab-549-add-account_created-property-to-onesignal-and-posthog' of github.com:Sababuu/frontend-main
e1cb0d02461210e10ac5d92aecab023f2e3e4429	not-for-merge	branch 'jakob/sab-595-add-csae-policy-to-app' of github.com:Sababuu/frontend-main
6b60094739a48ee34742c36f00dfd2822c4f3b64	not-for-merge	branch 'jakob/sab-728-add-is-this-person-verified-to-dialog-security-message' of github.com:Sababuu/frontend-main
45bd7d76a6688a560adaa50f14fa6ac2ef3897f7	not-for-merge	branch 'jakob/sab-753-fix-github-pr-descriptionlinear-comment-image-urls' of github.com:Sababuu/frontend-main
93dcfce44392cec26e2811493b3be3595578f156	not-for-merge	branch 'jakob/sab-761-fix-meta-data-tags-for-social-media-linking' of github.com:Sababuu/frontend-main
7f4e92e5a280b344bdfeade1984fd48a94355736	not-for-merge	branch 'jakob/sab-764-fix-the-deployment-slack-notification' of github.com:Sababuu/frontend-main
13c5e3007fdf8efb16a51a6c749dfc7094fb1636	not-for-merge	branch 'jakob/sab-766-opening-gallery-doesnt-open-first-photo' of github.com:Sababuu/frontend-main
246ffa6f6f8b516c8930dfea2a6ee6cdf4c3ce19	not-for-merge	branch 'jakob/sab-780-update-survey-fetch-logic-and-rating-surveys' of github.com:Sababuu/frontend-main
eb2fdf3e2a877c720a815676573734e536c660a4	not-for-merge	branch 'jakob/sab-784-update-survey-functionality' of github.com:Sababuu/frontend-main
2fd13313c8b7d2e37ae0f7bf57802971f4145d38	not-for-merge	branch 'jakob/sab-791-name-length-validation-not-working-properly-on-onboarding' of github.com:Sababuu/frontend-main
7f5037b1e1b48e183d48550e12055bd915ea8f09	not-for-merge	branch 'jakob/sab-792-split-my-profile-boost-settings-on-own-profile' of github.com:Sababuu/frontend-main
e900a0dd93e7d40c00d510f15c780cd0e40f1cd1	not-for-merge	branch 'main' of github.com:Sababuu/frontend-main
8028cfd2a868a01cffe9d3e3bb0d8e9f8d1ae95f	not-for-merge	branch 'optimize/hSTpwuMd_speed-optimization-getting-back-to-the-discover-page' of github.com:Sababuu/frontend-main
befc0c3ecffc8957346486d548fad94f3a08458a	not-for-merge	branch 'optimize/w56EGsUJ_bug-optimize-the-profile-creation-process' of github.com:Sababuu/frontend-main
8a2749d1ff387ef84bde1d76192e3682b72c016e	not-for-merge	branch 'prod' of github.com:Sababuu/frontend-main
4f2b9515052cb6d9467affa9593b1be63761a640	not-for-merge	branch 'refactoring-for-the-requestLocationAccess-function' of github.com:Sababuu/frontend-main
84dd49830778c29f0188625497dae8d83484577f	not-for-merge	branch 'release-04-03-2024' of github.com:Sababuu/frontend-main
5796a3e712c7e92ff86060f799f8fbd356a53058	not-for-merge	branch 'release-28-02-2024' of github.com:Sababuu/frontend-main
6209415dda4d11bd68694af67d3bb4a0919c784e	not-for-merge	branch 'release-9' of github.com:Sababuu/frontend-main
ee34f9b296d586eb754d0335325d754945a8235f	not-for-merge	branch 'release/2024-10-29' of github.com:Sababuu/frontend-main
f028b3c6fc30a48236e7ad957b3c09827a757d9d	not-for-merge	branch 'revert-209-tobey/sab-459-users-can-upload-gallery-pictures-in-verification-flow' of github.com:Sababuu/frontend-main
d2386847a572fe5c199a6da8d7f2ec3568bedae4	not-for-merge	branch 'revert-3-collins/sab-164-change-colour-scheme-from-red-to-orange' of github.com:Sababuu/frontend-main
9b0b700e458b329523bdbc91bb500f99d6bab296	not-for-merge	branch 'revert-367-dependabot/npm_and_yarn/multi-58f5cce575' of github.com:Sababuu/frontend-main
449a1a4f4d91dd36ac9aae2372ff7a700de11c3b	not-for-merge	branch 'revert-c904c9c4' of github.com:Sababuu/frontend-main
6d36b98adc24482ae8265b10a8a3bc8e0a9e8b62	not-for-merge	branch 'revert-test-push-notifications' of github.com:Sababuu/frontend-main
fe83af4b4ce24766ec05a673d2bbf6bcc7d0bdd3	not-for-merge	branch 'rework-infra' of github.com:Sababuu/frontend-main
69eeae258962363d5303db28aba84245d6eb3439	not-for-merge	branch 'sab-247-exploring-issue' of github.com:Sababuu/frontend-main
6806ced33924ba39dbcc67b6183ba63d550caf2c	not-for-merge	branch 'stage' of github.com:Sababuu/frontend-main
f761fb27bf88324274a6f66d7278abd5ebb3664f	not-for-merge	branch 'tobey/sab-270-use-cloudinary-for-who-liked-me-blurring' of github.com:Sababuu/frontend-main
762a6634eb9b1ad2e89e2ca056df61c525ade3b1	not-for-merge	branch 'tobey/sab-416-add-specific-network-error' of github.com:Sababuu/frontend-main
cd129ef6b3294ac3bc42b2515b23681ae9fd40f9	not-for-merge	branch 'tobey/sab-448-fix-local-storage-cache-issue-with-duplicate-image-checker' of github.com:Sababuu/frontend-main
06145de28100e36b888f65cc5972811e7c13a418	not-for-merge	branch 'tobey/sab-450-fix-help-tour-loading-outside-of-discovery-window' of github.com:Sababuu/frontend-main
7b1a400873e444bf59a4c357f755856c2f644532	not-for-merge	branch 'tobey/sab-458-add-ability-to-give-credits-once-card-is-answered' of github.com:Sababuu/frontend-main
ddd5b79ae562390f901ad81fe1777ae896921f42	not-for-merge	branch 'tobey/sab-459-users-can-upload-gallery-pictures-in-verification-flow' of github.com:Sababuu/frontend-main
428b2c7e7125713a4a7c1479b26547f800baad36	not-for-merge	branch 'tobey/sab-464-integrate-boosts-to-front-end' of github.com:Sababuu/frontend-main
450954c56bf11e97affe8bdfacfe54ef520f9244	not-for-merge	branch 'tobey/sab-469-add-birth-date-to-posthog-person-properties-for-age' of github.com:Sababuu/frontend-main
cb674de336f4aae4f5343c23ed3f0abd43e8d75c	not-for-merge	branch 'tobey/sab-480-flow-cards-not-showing' of github.com:Sababuu/frontend-main
4ad1056e82776f2cefee966ad3c1460883d7d6db	not-for-merge	branch 'tobey/sab-491-adjust-flowcard-copy-about-giving-users-credit' of github.com:Sababuu/frontend-main
eb5550449b853dbbabc84f84d4bda8567dbe75c3	not-for-merge	branch 'tobey/sab-521-apply-release-test-fixes' of github.com:Sababuu/frontend-main
5e9f2a06a3416adb56d4e468bb4b29dc1eb1e311	not-for-merge	branch 'tobey/sab-524-fix-typeerror-when-loading-profile-without-last-active-boost' of github.com:Sababuu/frontend-main
f1cb06b9f92e8044fabaf92d32542907675f825a	not-for-merge	branch 'tobey/sab-525-fix-wrong-date-and-missing-boost-marketing-info' of github.com:Sababuu/frontend-main
995e9f76945526aee9d951048ec7ce94cc506ad4	not-for-merge	branch 'tobey/sab-528-adjust-boosted-profiles-layout-and-button-positions' of github.com:Sababuu/frontend-main
53420d49aaf18010b8e93e5e6ae6ecd27722971d	not-for-merge	branch 'tobey/sab-532-truncate-long-messages-in-dialog-preview' of github.com:Sababuu/frontend-main
1ba016fc3b7adfc5b08f14deeeace525b7d9a75e	not-for-merge	branch 'typescript-migration' of github.com:Sababuu/frontend-main
491a33347e4928597e560702404af2e2a4a73cd8	not-for-merge	branch 'update-stage' of github.com:Sababuu/frontend-main
