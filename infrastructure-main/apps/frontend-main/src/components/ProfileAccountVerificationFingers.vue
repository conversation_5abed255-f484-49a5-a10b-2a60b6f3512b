<script setup>
import { reactive, onBeforeMount, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import bus from "@/bus";

import ButtonMain from "@/components/buttons/ButtonMain.vue";
import NavigationTop from "@/components/NavigationTop.vue";
import ButtonSquare from "@/components/buttons/ButtonSquare.vue";
import ArrowLeftIcon from "@/assets/icons/arrows/left.svg";
import RemoveImageBtnWrapper from "@/components/common/removeImageBtnWrapper.vue";
import PreloaderSpinner from "./PreloaderSpinner.vue";

import {
  profileVerificationRuleRequest,
  profileVerificationRequest,
} from "@/api/repositories/user";

import { useNativeCheckCameraPermission } from "@/composables/useNativeCheckCameraPermission";
import { registerNativeHandler } from "@/utils/reactNativeBus";

import { useProfileStore } from "@/stores/profile";
import { useProfilesStore } from "@/stores/profiles";

import { scrollToTop } from "@/utils/helpers";
import { logEvent } from "@/utils/tracking.js";
import trackedEvents from "@/utils/trackedEvents";

import SababuuImage from "@/components/SababuuImage.vue";
import InputFile from "@/components/InputFile.vue";
import VerificationRuleImage from "@/components/profile/VerificationRuleImage.vue";

const router = useRouter();
const route = useRoute();
const emit = defineEmits(["close"]);
const createObjectURL = window.URL.createObjectURL;
const profileStore = useProfileStore();
const profilesStore = useProfilesStore();

const { checkNativeCameraPermission } = useNativeCheckCameraPermission();

const state = reactive({
  rules: [],
  step: 1,
  selfie_photo1: undefined,
  selfie_photo2: undefined,
  isBooting: false,
  isNativeApp: window.SababuuNativeBridge !== undefined,
  updateNativeApp:
    window.SababuuNativeBridge !== undefined &&
    window.SababuuNativeBridge.requestCameraPermission === undefined,
  nativeCameraAccessGranted: false,
  loading: false,
  errors: {
    photoSize: false,
  },
});

onBeforeMount(async () => {
  state.isBooting = true;
  logEvent(trackedEvents.OWN_PROFILE_VERIFICATION_OPENED, {
    mode: "selfie",
  });

  try {
    const data = await profileVerificationRuleRequest();
    data.forEach((rule) => {
      state.rules.push(rule);
    });
  } catch (error) {
    console.log(error);
    bus.global.emit("notification.top:show", {
      type: "error",
      message:
        "Something went wrong while getting rule for verification, try again later",
    });
    emit("close");
  }

  if (window.SababuuNativeBridge?.getCameraStatus) {
    try {
      const { cameraAccessGranted } = await checkNativeCameraPermission();
      if (cameraAccessGranted) {
        state.nativeCameraAccessGranted = true;
      }
    } catch (error) {
      console.log(error);
      bus.global.emit("notification.top:show", {
        type: "error",
        title: "Unable to access your camera. Try again.",
      });
      emit("close");
    }
  }

  state.isBooting = false;
});

const derigesterRequestHandler = ref(null);
const requestNativeCameraAccess = () =>
  new Promise((resolve, reject) => {
    if (state.nativeCameraAccessGranted || !window.SababuuNativeBridge) {
      resolve();
      return;
    }

    derigesterRequestHandler.value = registerNativeHandler(
      "onRequestCameraPermission",
      (payload) => {
        derigesterRequestHandler.value();

        const { status, data, err } = payload;
        if (status === "success" && data && data.cameraAccessGranted) {
          resolve();
          state.nativeCameraAccessGranted = true;
          logEvent(trackedEvents.CAMERA_ACCESS_GRANTED);
        } else {
          logEvent(trackedEvents.CAMERA_ACCESS_FAILED);
          console.error("Camera access has not been granted!", err);
          reject("Camera access has not been granted!");
          bus.global.emit("notification.top:show", {
            type: "error",
            title: "Unable to access your camera. Try again.",
          });
          emit("close");
        }
      }
    );
    window.SababuuNativeBridge?.requestCameraPermission();
    logEvent(trackedEvents.CAMERA_ACCESS_REQUESTED);
  });

const openPlayStore = () => {
  window.open("https://play.google.com/store/apps/details?id=com.sababuu.twa");
};

const nextStep = () => {
  state.step = state.step + 1;
};

const prevStep = () => {
  if (state.step === 2) {
    state.step = 1;
  } else if (state.step === 1) {
    emit("close");
    bus.global.emit("modal.overlay:open", {
      name: "profile.verification.actions",
    });
  }
};

const redirectToDiscovery = () => {
  emit("close");

  if (route.name === "discovery") {
    // Remove the current card from the discovery feed and advance to the next one
    profilesStore.removeUpdateCard("verification", "slideRight");
    scrollToTop();
  }

  router.push({
    name: "discovery",
  });
};

const redirectToProfile = () => {
  emit("close");

  if (route.name === "discovery") {
    // Remove the current card from the discovery feed and advance to the next one
    profilesStore.removeUpdateCard("verification", "slideRight");
    scrollToTop();
  }

  router.push({
    name: "profile",
  });
};

const requestVerification = () => {
  // Prevent multiple simultaneous submissions
  if (state.loading) {
    return;
  }

  state.loading = true;

  return profileVerificationRequest({
    selfie_photo1: state.selfie_photo1,
    selfie_photo2: state.selfie_photo2,
  })
    .then((response) => {
      nextStep();
      if (response.user) {
        profileStore.setProfile(response.user);
      }
      if (state.step === 3) {
        logEvent(trackedEvents.OWN_PROFILE_VERIFICATION_ATTEMPTED, {
          mode: "selfie",
        });
        
        // Refresh profiles after requesting verification if not on discovery route
        if (route.name !== "discovery") {
          profilesStore.fetchInitialProfiles(null, true);
        }
      }
    })
    .catch(() => {
      bus.global.emit("notification.top:show", {
        type: "error",
        message:
          "Something went wrong while sending verification images, try again",
      });
      emit("close");
    })
    .finally(() => {
      state.loading = false;
    });
};

const removePhoto = (key) => {
  state[key] = undefined;
  state.errors.photoSize = false;
};

const uploadHandler = (event, key) => {
  state.errors.photoSize = false;
  validatePhotoSize(event, ********);
  if (!state.errors.photoSize) {
    state[key] = event;
  }
};
function validatePhotoSize(photo, size) {
  state.errors.photoSize = photo.size > size;
}
</script>

<template>
  <div class="verification">
    <div class="verification__content">
      <p>
        For account verification, please ensure your app is updated to the
        latest version.
      </p>
    </div>

    <!-- Loading state -->
    <div v-if="state.isNativeApp && state.updateNativeApp">
      <div class="update-app-prompt">
        <p>
          For account verification, please ensure your app is updated to the
          latest version. Thank you!
        </p>
        <ButtonMain class="update-prompt-button" @click="openPlayStore">
          Update App
        </ButtonMain>
        <ButtonMain
          styling="secondary"
          class="update-prompt-button"
          @click="prevStep"
        >
          Go Back
        </ButtonMain>
      </div>
    </div>
    <template v-else-if="state.isBooting">
      <div class="verification-preloader">
        <PreloaderSpinner :dark="true" />
      </div>
    </template>
    <template v-else>
      <navigation-top v-if="state.step !== 3">
        <button-square @click="prevStep">
          <ArrowLeftIcon />
        </button-square>
        <div class="header__title">
          <span v-if="state.step === 1">Photo (1/2)</span>
          <span v-if="state.step === 2">Photo (2/2)</span>
        </div>
      </navigation-top>

      <div v-if="state.step === 1">
        <div class="header">
          <div class="header__description">
            Verify your profile and help us keep Sababuu a safe community.
          </div>
        </div>
        <div class="uploader">
          <remove-image-btn-wrapper
            v-if="state.selfie_photo1"
            @confirm-deletion="removePhoto('selfie_photo1')"
          >
            <SababuuImage
              :source="createObjectURL(state.selfie_photo1)"
              class="preview__selfie"
            />
          </remove-image-btn-wrapper>
          <div v-else class="rule">
            <VerificationRuleImage
              :photo="state.rules[0]?.photo"
              :description="state.rules[0]?.description"
            />
          </div>
          <div v-if="!state.selfie_photo1" class="uploader__tip">
            {{ state.rules[0]?.description }}
          </div>
          <div v-if="!state.selfie_photo1" class="uploader__input">
            <InputFile
              accept="image/*"
              camera-only
              :resize-to-max="600"
              :request-native-camera="requestNativeCameraAccess"
              @upload="uploadHandler($event, 'selfie_photo1')"
            />
          </div>
          <!-- Show error here... -->
          <button-main
            v-if="state.selfie_photo1"
            :disabled="
              !state.selfie_photo1 ||
              (state.selfie_photo1 && !state.rules.length) ||
              state.loading
            "
            :loading="state.selfie_photo1 && !state.rules.length"
            @click="nextStep"
          >
            Continue
          </button-main>
        </div>
      </div>

      <div v-if="state.step === 2">
        <div class="header">
          <div class="header__description">
            Verified users get <strong>2x</strong> more likes and chats.
          </div>
        </div>
        <div class="uploader">
          <remove-image-btn-wrapper
            v-if="state.selfie_photo2"
            @confirm-deletion="removePhoto('selfie_photo2')"
          >
            <SababuuImage
              :source="createObjectURL(state.selfie_photo2)"
              class="preview__selfie"
            />
          </remove-image-btn-wrapper>
          <div v-else>
            <VerificationRuleImage
              :photo="state.rules[1]?.photo"
              :description="state.rules[1]?.description"
            />
          </div>
          <div v-if="!state.selfie_photo2" class="uploader__tip">
            {{ state.rules[1]?.description }}
          </div>
          <div v-if="!state.selfie_photo2" class="uploader__input">
            <InputFile
              accept="image/*"
              camera-only
              :request-native-camera="requestNativeCameraAccess"
              @upload="uploadHandler($event, 'selfie_photo2')"
            />
          </div>
          <!-- Show error here ... -->
          <button-main
            v-if="state.selfie_photo2"
            :disabled="!state.selfie_photo2 || state.loading"
            :loading="state.loading"
            @click="requestVerification"
          >
            Continue
          </button-main>
        </div>
      </div>

      <div v-if="state.step === 3" class="wrapper">
        <div class="header">
          <div class="header__title">Thank You!</div>
          <div class="header__description">
            We'll notify you as soon as your profile is verified.
          </div>
        </div>
        <div class="verification__buttons">
          <button-main @click="redirectToProfile">
            Go to your profile
          </button-main>
          <button-main styling="secondary" @click="redirectToDiscovery">
            Discover profiles
          </button-main>
        </div>
      </div>
    </template>
  </div>
</template>

<style lang="scss" scoped>
.verification {
  width: 100%;
  height: 100%;
}

.wrapper {
  width: 100%;
  height: 100%;
  padding: 20px;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  row-gap: 16px;
}

.header {
  margin-top: 1rem;
  text-align: center;
  display: flex;
  flex-direction: column;
  row-gap: 20px;
}

.header__title {
  font-weight: 700;
  font-size: 24px;
}

.uploader {
  width: 100%;
  display: flex;
  flex-direction: column;
  row-gap: 15px;
  justify-content: center;
  align-items: center;
  padding-top: 16px;

  &__hint {
    color: rgba(0, 0, 0, 0.7);
    text-align: center;

    &.error {
      font-weight: 400;
      color: red;
    }
  }
}

.uploader__tip {
  font-size: 14px;
  font-weight: 600;
  height: 15px;
  text-decoration: solid underline 2px;
  color: rgba(233, 64, 87, 1);
}

.uploader__input {
  width: 100%;
}

.preview__selfie {
  width: 330px;
  border-radius: 15px;
}

.img-with-removing-section {
  margin-top: 35px;
}

.verification__buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 10px;
}

.update-app-prompt {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  p {
    font-size: 1.2rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 0.5rem;
  }

  .update-prompt-button {
    margin-top: 1rem;
  }
}

.verification-preloader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.verification-preloader__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.verification-preloader__spinner {
  width: 50px;
  height: 50px;
}

.verification-preloader__content p {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.verification__example {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  text-align: center;
}

.verification__example-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 12px;
}

.verification__example-avatar {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-preview {
  position: relative;
  width: 60px;
  height: 60px;
}

.avatar-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: 3px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.verification-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 24px;
  height: 24px;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.verification-badge svg {
  width: 14px;
  height: 14px;
  color: white;
}
</style>
