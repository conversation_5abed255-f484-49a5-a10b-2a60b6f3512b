<template>
  <div v-if="verifyAction" class="profile__verification">
    <div>
      <ArrowCheckVerifiedIcon v-if="profileStore.profile.person_verification" />
      <p class="profile__verification__prompt" v-html="verifyAction.prompt">
      </p>
    </div>
    <span
      v-if="verifyAction.action"
      class="profile__verification__link"
      @click="verifyAction.action"
    >
      Verify account for free
    </span>
    <p v-if="verifyAction.action" class="profile__verification__benefits">
      Verified accounts get <strong>2x</strong> more likes and chats.
    </p>
  </div>
  <ModalOverlay
    v-if="!profileStore.profile.person_verification"
    :padding-s-m="true"
    :name="verifyFingersModal"
  >
    <ProfileAccountVerificationFingers
      @close="() => closeModal(verifyFingersModal)"
    />
  </ModalOverlay>
</template>

<script setup>
import { computed } from "vue";
import bus from "@/bus";

import { useProfileStore } from "@/stores/profile";

import ModalOverlay from "@/components/ModalOverlay.vue";
import ProfileAccountVerificationFingers from "@/components/ProfileAccountVerificationFingers.vue";

import ArrowCheckVerifiedIcon from "@/assets/icons/arrow-check-verified.svg";

const profileStore = useProfileStore();

const verifyFingersModal = "profile.verification";

const verifyAction = computed(() => {
  if (!profileStore.profile.verification_status) {
    return {
      prompt:
        "Your account is <strong>not verified</strong>. Verification is <strong>free</strong>, quick, and easy:",
      action: () => openModal(),
    };
  } else if (profileStore.profile.verification_status === "rejected") {
    return {
      prompt:
        "Your verification request was rejected. Please send a new request and ensure that your selfies are clear and similar to the photos on your profile.",
      action: () => openModal(),
    };
  } else if (profileStore.profile.verification_status === "pending") {
    return {
      prompt:
        "We're reviewing your free verification request! It's normally very quick. You'll be notified when the review is complete.",
      action: null,
    };
  } else if (profileStore.profile.verification_status === "revoked") {
    return {
      prompt:
        "Your verification was re-submitted! We'll notify you when the review is complete.",
      action: null,
    };
  } else if (profileStore.profile.verification_status === "approved") {
    return {
      prompt: "Your profile is verified! 🎉",
      action: null,
    };
  } else {
    return null;
  }
});

const openModal = () => {
  bus.global.emit("modal.overlay:open", { name: "profile.verification" });
};

const closeModal = () => {
  bus.global.emit("modal.overlay:close", { name: "profile.verification" });
};
</script>

<style lang="scss" scoped>
.profile__verification {
  box-shadow: $profile-card-item-shadow-sm;
  background-color: white !important;
  border: 1px solid $profile-card-item-border-color;
  border-radius: 5px;
  padding: 16px;

  & > div {
    display: flex;
    flex-direction: row;
    column-gap: 0.3rem;
  }

  &__prompt {
    font-size: 14px;
  }

  &__link {
    color: $primary-color;
    font-size: 14px;
    border-bottom: 1px solid;
    border-color: $primary-color;
    display: inline-block;
    margin-top: 0.5rem;
  }

  &__benefits {
    font-size: 12px;
    color: #666;
    margin-top: 0.5rem;
    font-style: italic;
  }
}
</style>
