import {
  quotaErrorCallbacks
} from "./chunk-FWQKDA3S.js";
import {
  finalAssertExports,
  logger
} from "./chunk-FWFYZ7QY.js";

// node_modules/workbox-core/registerQuotaErrorCallback.js
function registerQuotaErrorCallback(callback) {
  if (true) {
    finalAssertExports.isType(callback, "function", {
      moduleName: "workbox-core",
      funcName: "register",
      paramName: "callback"
    });
  }
  quotaErrorCallbacks.add(callback);
  if (true) {
    logger.log("Registered a callback to respond to quota errors.", callback);
  }
}

// node_modules/workbox-core/_private/dontWaitFor.js
function dontWaitFor(promise) {
  void promise.then(() => {
  });
}

export {
  registerQuotaErrorCallback,
  dontWaitFor
};
//# sourceMappingURL=chunk-W4QEL4OB.js.map
