{"version": 3, "sources": ["../../workbox-strategies/utils/messages.js", "../../workbox-strategies/CacheFirst.js", "../../workbox-strategies/CacheOnly.js", "../../workbox-strategies/plugins/cacheOkAndOpaquePlugin.js", "../../workbox-strategies/NetworkFirst.js", "../../workbox-strategies/NetworkOnly.js", "../../workbox-strategies/StaleWhileRevalidate.js"], "sourcesContent": ["/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport '../_version.js';\nexport const messages = {\n    strategyStart: (strategyName, request) => `Using ${strategyName} to respond to '${getFriendlyURL(request.url)}'`,\n    printFinalResponse: (response) => {\n        if (response) {\n            logger.groupCollapsed(`View the final response here.`);\n            logger.log(response || '[No response returned]');\n            logger.groupEnd();\n        }\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-first-falling-back-to-network)\n * request strategy.\n *\n * A cache first strategy is useful for assets that have been revisioned,\n * such as URLs like `/styles/example.a8f5f1.css`, since they\n * can be cached for long periods of time.\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheFirst extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        let response = await handler.cacheMatch(request);\n        let error = undefined;\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will respond with a network request.`);\n            }\n            try {\n                response = await handler.fetchAndCachePut(request);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Got response from network.`);\n                }\n                else {\n                    logs.push(`Unable to get a response from the network.`);\n                }\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}' cache.`);\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { CacheFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a [cache-only](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#cache-only)\n * request strategy.\n *\n * This class is useful if you want to take advantage of any\n * [Workbox plugins](https://developer.chrome.com/docs/workbox/using-plugins/).\n *\n * If there is no cache match, this will throw a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass CacheOnly extends Strategy {\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'makeRequest',\n                paramName: 'request',\n            });\n        }\n        const response = await handler.cacheMatch(request);\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            if (response) {\n                logger.log(`Found a cached response in the '${this.cacheName}' ` + `cache.`);\n                messages.printFinalResponse(response);\n            }\n            else {\n                logger.log(`No response found in the '${this.cacheName}' cache.`);\n            }\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n}\nexport { CacheOnly };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nexport const cacheOkAndOpaquePlugin = {\n    /**\n     * Returns a valid response (to allow caching) if the status is 200 (OK) or\n     * 0 (opaque).\n     *\n     * @param {Object} options\n     * @param {Response} options.response\n     * @return {Response|null}\n     *\n     * @private\n     */\n    cacheWillUpdate: async ({ response }) => {\n        if (response.status === 200 || response.status === 0) {\n            return response;\n        }\n        return null;\n    },\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network first](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-first-falling-back-to-cache)\n * request strategy.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkFirst extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will fallback to the cache.\n     *\n     * This option can be used to combat\n     * \"[lie-fi]{@link https://developers.google.com/web/fundamentals/performance/poor-connectivity/#lie-fi}\"\n     * scenarios.\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n        if (process.env.NODE_ENV !== 'production') {\n            if (this._networkTimeoutSeconds) {\n                assert.isType(this._networkTimeoutSeconds, 'number', {\n                    moduleName: 'workbox-strategies',\n                    className: this.constructor.name,\n                    funcName: 'constructor',\n                    paramName: 'networkTimeoutSeconds',\n                });\n            }\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'makeRequest',\n            });\n        }\n        const promises = [];\n        let timeoutId;\n        if (this._networkTimeoutSeconds) {\n            const { id, promise } = this._getTimeoutPromise({ request, logs, handler });\n            timeoutId = id;\n            promises.push(promise);\n        }\n        const networkPromise = this._getNetworkPromise({\n            timeoutId,\n            request,\n            logs,\n            handler,\n        });\n        promises.push(networkPromise);\n        const response = await handler.waitUntil((async () => {\n            // Promise.race() will resolve as soon as the first promise resolves.\n            return ((await handler.waitUntil(Promise.race(promises))) ||\n                // If Promise.race() resolved with null, it might be due to a network\n                // timeout + a cache miss. If that were to happen, we'd rather wait until\n                // the networkPromise resolves instead of returning null.\n                // Note that it's fine to await an already-resolved promise, so we don't\n                // have to check to see if it's still \"in flight\".\n                (await networkPromise));\n        })());\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url });\n        }\n        return response;\n    }\n    /**\n     * @param {Object} options\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs array\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    _getTimeoutPromise({ request, logs, handler, }) {\n        let timeoutId;\n        const timeoutPromise = new Promise((resolve) => {\n            const onNetworkTimeout = async () => {\n                if (process.env.NODE_ENV !== 'production') {\n                    logs.push(`Timing out the network response at ` +\n                        `${this._networkTimeoutSeconds} seconds.`);\n                }\n                resolve(await handler.cacheMatch(request));\n            };\n            timeoutId = setTimeout(onNetworkTimeout, this._networkTimeoutSeconds * 1000);\n        });\n        return {\n            promise: timeoutPromise,\n            id: timeoutId,\n        };\n    }\n    /**\n     * @param {Object} options\n     * @param {number|undefined} options.timeoutId\n     * @param {Request} options.request\n     * @param {Array} options.logs A reference to the logs Array.\n     * @param {Event} options.event\n     * @return {Promise<Response>}\n     *\n     * @private\n     */\n    async _getNetworkPromise({ timeoutId, request, logs, handler, }) {\n        let error;\n        let response;\n        try {\n            response = await handler.fetchAndCachePut(request);\n        }\n        catch (fetchError) {\n            if (fetchError instanceof Error) {\n                error = fetchError;\n            }\n        }\n        if (timeoutId) {\n            clearTimeout(timeoutId);\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            if (response) {\n                logs.push(`Got response from network.`);\n            }\n            else {\n                logs.push(`Unable to get a response from the network. Will respond ` +\n                    `with a cached response.`);\n            }\n        }\n        if (error || !response) {\n            response = await handler.cacheMatch(request);\n            if (process.env.NODE_ENV !== 'production') {\n                if (response) {\n                    logs.push(`Found a cached response in the '${this.cacheName}'` + ` cache.`);\n                }\n                else {\n                    logs.push(`No response found in the '${this.cacheName}' cache.`);\n                }\n            }\n        }\n        return response;\n    }\n}\nexport { NetworkFirst };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [network-only](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#network-only)\n * request strategy.\n *\n * This class is useful if you want to take advantage of any\n * [Workbox plugins](https://developer.chrome.com/docs/workbox/using-plugins/).\n *\n * If the network request fails, this will throw a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass NetworkOnly extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {number} [options.networkTimeoutSeconds] If set, any network requests\n     * that fail to respond within the timeout will result in a network error.\n     */\n    constructor(options = {}) {\n        super(options);\n        this._networkTimeoutSeconds = options.networkTimeoutSeconds || 0;\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: '_handle',\n                paramName: 'request',\n            });\n        }\n        let error = undefined;\n        let response;\n        try {\n            const promises = [\n                handler.fetch(request),\n            ];\n            if (this._networkTimeoutSeconds) {\n                const timeoutPromise = timeout(this._networkTimeoutSeconds * 1000);\n                promises.push(timeoutPromise);\n            }\n            response = await Promise.race(promises);\n            if (!response) {\n                throw new Error(`Timed out the network response after ` +\n                    `${this._networkTimeoutSeconds} seconds.`);\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                error = err;\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            if (response) {\n                logger.log(`Got response from network.`);\n            }\n            else {\n                logger.log(`Unable to get a response from the network.`);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { NetworkOnly };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { cacheOkAndOpaquePlugin } from './plugins/cacheOkAndOpaquePlugin.js';\nimport { Strategy } from './Strategy.js';\nimport { messages } from './utils/messages.js';\nimport './_version.js';\n/**\n * An implementation of a\n * [stale-while-revalidate](https://developer.chrome.com/docs/workbox/caching-strategies-overview/#stale-while-revalidate)\n * request strategy.\n *\n * Resources are requested from both the cache and the network in parallel.\n * The strategy will respond with the cached version if available, otherwise\n * wait for the network response. The cache is updated with the network response\n * with each successful request.\n *\n * By default, this strategy will cache responses with a 200 status code as\n * well as [opaque responses](https://developer.chrome.com/docs/workbox/caching-resources-during-runtime/#opaque-responses).\n * Opaque responses are cross-origin requests where the response doesn't\n * support [CORS](https://enable-cors.org/).\n *\n * If the network request fails, and there is no cache match, this will throw\n * a `WorkboxError` exception.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-strategies\n */\nclass StaleWhileRevalidate extends Strategy {\n    /**\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] [`CacheQueryOptions`](https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions)\n     */\n    constructor(options = {}) {\n        super(options);\n        // If this instance contains no plugins with a 'cacheWillUpdate' callback,\n        // prepend the `cacheOkAndOpaquePlugin` plugin to the plugins list.\n        if (!this.plugins.some((p) => 'cacheWillUpdate' in p)) {\n            this.plugins.unshift(cacheOkAndOpaquePlugin);\n        }\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const logs = [];\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-strategies',\n                className: this.constructor.name,\n                funcName: 'handle',\n                paramName: 'request',\n            });\n        }\n        const fetchAndCachePromise = handler.fetchAndCachePut(request).catch(() => {\n            // Swallow this error because a 'no-response' error will be thrown in\n            // main handler return flow. This will be in the `waitUntil()` flow.\n        });\n        void handler.waitUntil(fetchAndCachePromise);\n        let response = await handler.cacheMatch(request);\n        let error;\n        if (response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`Found a cached response in the '${this.cacheName}'` +\n                    ` cache. Will update with the network response in the background.`);\n            }\n        }\n        else {\n            if (process.env.NODE_ENV !== 'production') {\n                logs.push(`No response found in the '${this.cacheName}' cache. ` +\n                    `Will wait for the network response.`);\n            }\n            try {\n                // NOTE(philipwalton): Really annoying that we have to type cast here.\n                // https://github.com/microsoft/TypeScript/issues/20006\n                response = (await fetchAndCachePromise);\n            }\n            catch (err) {\n                if (err instanceof Error) {\n                    error = err;\n                }\n            }\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.groupCollapsed(messages.strategyStart(this.constructor.name, request));\n            for (const log of logs) {\n                logger.log(log);\n            }\n            messages.printFinalResponse(response);\n            logger.groupEnd();\n        }\n        if (!response) {\n            throw new WorkboxError('no-response', { url: request.url, error });\n        }\n        return response;\n    }\n}\nexport { StaleWhileRevalidate };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAUO,IAAM,WAAW;AAAA,EACpB,eAAe,CAAC,cAAc,YAAY,SAAS,YAAY,mBAAmB,eAAe,QAAQ,GAAG,CAAC;AAAA,EAC7G,oBAAoB,CAAC,aAAa;AAC9B,QAAI,UAAU;AACV,aAAO,eAAe,+BAA+B;AACrD,aAAO,IAAI,YAAY,wBAAwB;AAC/C,aAAO,SAAS;AAAA,IACpB;AAAA,EACJ;AACJ;;;ACQA,IAAM,aAAN,cAAyB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9B,MAAM,QAAQ,SAAS,SAAS;AAC5B,UAAM,OAAO,CAAC;AACd,QAAI,MAAuC;AACvC,yBAAO,WAAW,SAAS,SAAS;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW,KAAK,YAAY;AAAA,QAC5B,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,QAAI,WAAW,MAAM,QAAQ,WAAW,OAAO;AAC/C,QAAI,QAAQ;AACZ,QAAI,CAAC,UAAU;AACX,UAAI,MAAuC;AACvC,aAAK,KAAK,6BAA6B,KAAK,SAAS,+CACX;AAAA,MAC9C;AACA,UAAI;AACA,mBAAW,MAAM,QAAQ,iBAAiB,OAAO;AAAA,MACrD,SACO,KAAK;AACR,YAAI,eAAe,OAAO;AACtB,kBAAQ;AAAA,QACZ;AAAA,MACJ;AACA,UAAI,MAAuC;AACvC,YAAI,UAAU;AACV,eAAK,KAAK,4BAA4B;AAAA,QAC1C,OACK;AACD,eAAK,KAAK,4CAA4C;AAAA,QAC1D;AAAA,MACJ;AAAA,IACJ,OACK;AACD,UAAI,MAAuC;AACvC,aAAK,KAAK,mCAAmC,KAAK,SAAS,UAAU;AAAA,MACzE;AAAA,IACJ;AACA,QAAI,MAAuC;AACvC,aAAO,eAAe,SAAS,cAAc,KAAK,YAAY,MAAM,OAAO,CAAC;AAC5E,iBAAW,OAAO,MAAM;AACpB,eAAO,IAAI,GAAG;AAAA,MAClB;AACA,eAAS,mBAAmB,QAAQ;AACpC,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,aAAa,eAAe,EAAE,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IACrE;AACA,WAAO;AAAA,EACX;AACJ;;;AC9DA,IAAM,YAAN,cAAwB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,MAAM,QAAQ,SAAS,SAAS;AAC5B,QAAI,MAAuC;AACvC,yBAAO,WAAW,SAAS,SAAS;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW,KAAK,YAAY;AAAA,QAC5B,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,WAAW,MAAM,QAAQ,WAAW,OAAO;AACjD,QAAI,MAAuC;AACvC,aAAO,eAAe,SAAS,cAAc,KAAK,YAAY,MAAM,OAAO,CAAC;AAC5E,UAAI,UAAU;AACV,eAAO,IAAI,mCAAmC,KAAK,SAAS,UAAe;AAC3E,iBAAS,mBAAmB,QAAQ;AAAA,MACxC,OACK;AACD,eAAO,IAAI,6BAA6B,KAAK,SAAS,UAAU;AAAA,MACpE;AACA,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,aAAa,eAAe,EAAE,KAAK,QAAQ,IAAI,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACX;AACJ;;;ACnDO,IAAM,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWlC,iBAAiB,OAAO,EAAE,SAAS,MAAM;AACrC,QAAI,SAAS,WAAW,OAAO,SAAS,WAAW,GAAG;AAClD,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;;;ACKA,IAAM,eAAN,cAA2B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBhC,YAAY,UAAU,CAAC,GAAG;AACtB,UAAM,OAAO;AAGb,QAAI,CAAC,KAAK,QAAQ,KAAK,CAAC,MAAM,qBAAqB,CAAC,GAAG;AACnD,WAAK,QAAQ,QAAQ,sBAAsB;AAAA,IAC/C;AACA,SAAK,yBAAyB,QAAQ,yBAAyB;AAC/D,QAAI,MAAuC;AACvC,UAAI,KAAK,wBAAwB;AAC7B,2BAAO,OAAO,KAAK,wBAAwB,UAAU;AAAA,UACjD,YAAY;AAAA,UACZ,WAAW,KAAK,YAAY;AAAA,UAC5B,UAAU;AAAA,UACV,WAAW;AAAA,QACf,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ,SAAS,SAAS;AAC5B,UAAM,OAAO,CAAC;AACd,QAAI,MAAuC;AACvC,yBAAO,WAAW,SAAS,SAAS;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW,KAAK,YAAY;AAAA,QAC5B,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,WAAW,CAAC;AAClB,QAAI;AACJ,QAAI,KAAK,wBAAwB;AAC7B,YAAM,EAAE,IAAI,QAAQ,IAAI,KAAK,mBAAmB,EAAE,SAAS,MAAM,QAAQ,CAAC;AAC1E,kBAAY;AACZ,eAAS,KAAK,OAAO;AAAA,IACzB;AACA,UAAM,iBAAiB,KAAK,mBAAmB;AAAA,MAC3C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,aAAS,KAAK,cAAc;AAC5B,UAAM,WAAW,MAAM,QAAQ,WAAW,YAAY;AAElD,aAAS,MAAM,QAAQ,UAAU,QAAQ,KAAK,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAMlD,MAAM;AAAA,IACf,GAAG,CAAC;AACJ,QAAI,MAAuC;AACvC,aAAO,eAAe,SAAS,cAAc,KAAK,YAAY,MAAM,OAAO,CAAC;AAC5E,iBAAW,OAAO,MAAM;AACpB,eAAO,IAAI,GAAG;AAAA,MAClB;AACA,eAAS,mBAAmB,QAAQ;AACpC,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,aAAa,eAAe,EAAE,KAAK,QAAQ,IAAI,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,mBAAmB,EAAE,SAAS,MAAM,QAAS,GAAG;AAC5C,QAAI;AACJ,UAAM,iBAAiB,IAAI,QAAQ,CAAC,YAAY;AAC5C,YAAM,mBAAmB,YAAY;AACjC,YAAI,MAAuC;AACvC,eAAK,KAAK,sCACH,KAAK,sBAAsB,WAAW;AAAA,QACjD;AACA,gBAAQ,MAAM,QAAQ,WAAW,OAAO,CAAC;AAAA,MAC7C;AACA,kBAAY,WAAW,kBAAkB,KAAK,yBAAyB,GAAI;AAAA,IAC/E,CAAC;AACD,WAAO;AAAA,MACH,SAAS;AAAA,MACT,IAAI;AAAA,IACR;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,mBAAmB,EAAE,WAAW,SAAS,MAAM,QAAS,GAAG;AAC7D,QAAI;AACJ,QAAI;AACJ,QAAI;AACA,iBAAW,MAAM,QAAQ,iBAAiB,OAAO;AAAA,IACrD,SACO,YAAY;AACf,UAAI,sBAAsB,OAAO;AAC7B,gBAAQ;AAAA,MACZ;AAAA,IACJ;AACA,QAAI,WAAW;AACX,mBAAa,SAAS;AAAA,IAC1B;AACA,QAAI,MAAuC;AACvC,UAAI,UAAU;AACV,aAAK,KAAK,4BAA4B;AAAA,MAC1C,OACK;AACD,aAAK,KAAK,iFACmB;AAAA,MACjC;AAAA,IACJ;AACA,QAAI,SAAS,CAAC,UAAU;AACpB,iBAAW,MAAM,QAAQ,WAAW,OAAO;AAC3C,UAAI,MAAuC;AACvC,YAAI,UAAU;AACV,eAAK,KAAK,mCAAmC,KAAK,SAAS,UAAe;AAAA,QAC9E,OACK;AACD,eAAK,KAAK,6BAA6B,KAAK,SAAS,UAAU;AAAA,QACnE;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;;;ACxKA,IAAM,cAAN,cAA0B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY/B,YAAY,UAAU,CAAC,GAAG;AACtB,UAAM,OAAO;AACb,SAAK,yBAAyB,QAAQ,yBAAyB;AAAA,EACnE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ,SAAS,SAAS;AAC5B,QAAI,MAAuC;AACvC,yBAAO,WAAW,SAAS,SAAS;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW,KAAK,YAAY;AAAA,QAC5B,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,QAAI,QAAQ;AACZ,QAAI;AACJ,QAAI;AACA,YAAM,WAAW;AAAA,QACb,QAAQ,MAAM,OAAO;AAAA,MACzB;AACA,UAAI,KAAK,wBAAwB;AAC7B,cAAM,iBAAiB,QAAQ,KAAK,yBAAyB,GAAI;AACjE,iBAAS,KAAK,cAAc;AAAA,MAChC;AACA,iBAAW,MAAM,QAAQ,KAAK,QAAQ;AACtC,UAAI,CAAC,UAAU;AACX,cAAM,IAAI,MAAM,wCACT,KAAK,sBAAsB,WAAW;AAAA,MACjD;AAAA,IACJ,SACO,KAAK;AACR,UAAI,eAAe,OAAO;AACtB,gBAAQ;AAAA,MACZ;AAAA,IACJ;AACA,QAAI,MAAuC;AACvC,aAAO,eAAe,SAAS,cAAc,KAAK,YAAY,MAAM,OAAO,CAAC;AAC5E,UAAI,UAAU;AACV,eAAO,IAAI,4BAA4B;AAAA,MAC3C,OACK;AACD,eAAO,IAAI,4CAA4C;AAAA,MAC3D;AACA,eAAS,mBAAmB,QAAQ;AACpC,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,aAAa,eAAe,EAAE,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IACrE;AACA,WAAO;AAAA,EACX;AACJ;;;AC7DA,IAAM,uBAAN,cAAmC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcxC,YAAY,UAAU,CAAC,GAAG;AACtB,UAAM,OAAO;AAGb,QAAI,CAAC,KAAK,QAAQ,KAAK,CAAC,MAAM,qBAAqB,CAAC,GAAG;AACnD,WAAK,QAAQ,QAAQ,sBAAsB;AAAA,IAC/C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ,SAAS,SAAS;AAC5B,UAAM,OAAO,CAAC;AACd,QAAI,MAAuC;AACvC,yBAAO,WAAW,SAAS,SAAS;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW,KAAK,YAAY;AAAA,QAC5B,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,uBAAuB,QAAQ,iBAAiB,OAAO,EAAE,MAAM,MAAM;AAAA,IAG3E,CAAC;AACD,SAAK,QAAQ,UAAU,oBAAoB;AAC3C,QAAI,WAAW,MAAM,QAAQ,WAAW,OAAO;AAC/C,QAAI;AACJ,QAAI,UAAU;AACV,UAAI,MAAuC;AACvC,aAAK,KAAK,mCAAmC,KAAK,SAAS,mEACW;AAAA,MAC1E;AAAA,IACJ,OACK;AACD,UAAI,MAAuC;AACvC,aAAK,KAAK,6BAA6B,KAAK,SAAS,8CACZ;AAAA,MAC7C;AACA,UAAI;AAGA,mBAAY,MAAM;AAAA,MACtB,SACO,KAAK;AACR,YAAI,eAAe,OAAO;AACtB,kBAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAuC;AACvC,aAAO,eAAe,SAAS,cAAc,KAAK,YAAY,MAAM,OAAO,CAAC;AAC5E,iBAAW,OAAO,MAAM;AACpB,eAAO,IAAI,GAAG;AAAA,MAClB;AACA,eAAS,mBAAmB,QAAQ;AACpC,aAAO,SAAS;AAAA,IACpB;AACA,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,aAAa,eAAe,EAAE,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IACrE;AACA,WAAO;AAAA,EACX;AACJ;", "names": []}