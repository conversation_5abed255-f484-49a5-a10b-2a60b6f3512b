{"version": 3, "sources": ["../../workbox-routing/NavigationRoute.js", "../../workbox-routing/setCatchHandler.js", "../../workbox-routing/setDefaultHandler.js"], "sourcesContent": ["/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * NavigationRoute makes it easy to create a\n * {@link workbox-routing.Route} that matches for browser\n * [navigation requests]{@link https://developers.google.com/web/fundamentals/primers/service-workers/high-performance-loading#first_what_are_navigation_requests}.\n *\n * It will only match incoming Requests whose\n * {@link https://fetch.spec.whatwg.org/#concept-request-mode|mode}\n * is set to `navigate`.\n *\n * You can optionally only apply this route to a subset of navigation requests\n * by using one or both of the `denylist` and `allowlist` parameters.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass NavigationRoute extends Route {\n    /**\n     * If both `denylist` and `allowlist` are provided, the `denylist` will\n     * take precedence and the request will not match this route.\n     *\n     * The regular expressions in `allowlist` and `denylist`\n     * are matched against the concatenated\n     * [`pathname`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/pathname}\n     * and [`search`]{@link https://developer.mozilla.org/en-US/docs/Web/API/HTMLHyperlinkElementUtils/search}\n     * portions of the requested URL.\n     *\n     * *Note*: These RegExps may be evaluated against every destination URL during\n     * a navigation. Avoid using\n     * [complex RegExps](https://github.com/GoogleChrome/workbox/issues/3077),\n     * or else your users may see delays when navigating your site.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {Object} options\n     * @param {Array<RegExp>} [options.denylist] If any of these patterns match,\n     * the route will not handle the request (even if a allowlist RegExp matches).\n     * @param {Array<RegExp>} [options.allowlist=[/./]] If any of these patterns\n     * match the URL's pathname and search parameter, the route will handle the\n     * request (assuming the denylist doesn't match).\n     */\n    constructor(handler, { allowlist = [/./], denylist = [] } = {}) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArrayOfClass(allowlist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.allowlist',\n            });\n            assert.isArrayOfClass(denylist, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'NavigationRoute',\n                funcName: 'constructor',\n                paramName: 'options.denylist',\n            });\n        }\n        super((options) => this._match(options), handler);\n        this._allowlist = allowlist;\n        this._denylist = denylist;\n    }\n    /**\n     * Routes match handler.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {Request} options.request\n     * @return {boolean}\n     *\n     * @private\n     */\n    _match({ url, request }) {\n        if (request && request.mode !== 'navigate') {\n            return false;\n        }\n        const pathnameAndSearch = url.pathname + url.search;\n        for (const regExp of this._denylist) {\n            if (regExp.test(pathnameAndSearch)) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                        `being used, since the URL matches this denylist pattern: ` +\n                        `${regExp.toString()}`);\n                }\n                return false;\n            }\n        }\n        if (this._allowlist.some((regExp) => regExp.test(pathnameAndSearch))) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`The navigation route ${pathnameAndSearch} ` + `is being used.`);\n            }\n            return true;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(`The navigation route ${pathnameAndSearch} is not ` +\n                `being used, since the URL being navigated to doesn't ` +\n                `match the allowlist.`);\n        }\n        return false;\n    }\n}\nexport { NavigationRoute };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * If a Route throws an error while handling a request, this `handler`\n * will be called and given a chance to provide a response.\n *\n * @param {workbox-routing~handlerCallback} handler A callback\n * function that returns a Promise resulting in a Response.\n *\n * @memberof workbox-routing\n */\nfunction setCatchHandler(handler) {\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.setCatchHandler(handler);\n}\nexport { setCatchHandler };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Define a default `handler` that's called when no routes explicitly\n * match the incoming request.\n *\n * Without a default handler, unmatched requests will go against the\n * network as if there were no service worker present.\n *\n * @param {workbox-routing~handlerCallback} handler A callback\n * function that returns a Promise resulting in a Response.\n *\n * @memberof workbox-routing\n */\nfunction setDefaultHandler(handler) {\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.setDefaultHandler(handler);\n}\nexport { setDefaultHandler };\n"], "mappings": ";;;;;;;;;;;;;;AA0BA,IAAM,kBAAN,cAA8B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyBhC,YAAY,SAAS,EAAE,YAAY,CAAC,GAAG,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC,GAAG;AAC5D,QAAI,MAAuC;AACvC,yBAAO,eAAe,WAAW,QAAQ;AAAA,QACrC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AACD,yBAAO,eAAe,UAAU,QAAQ;AAAA,QACpC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,CAAC,YAAY,KAAK,OAAO,OAAO,GAAG,OAAO;AAChD,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,EAAE,KAAK,QAAQ,GAAG;AACrB,QAAI,WAAW,QAAQ,SAAS,YAAY;AACxC,aAAO;AAAA,IACX;AACA,UAAM,oBAAoB,IAAI,WAAW,IAAI;AAC7C,eAAW,UAAU,KAAK,WAAW;AACjC,UAAI,OAAO,KAAK,iBAAiB,GAAG;AAChC,YAAI,MAAuC;AACvC,iBAAO,IAAI,wBAAwB,iBAAiB,oEAE7C,OAAO,SAAS,CAAC,EAAE;AAAA,QAC9B;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AACA,QAAI,KAAK,WAAW,KAAK,CAAC,WAAW,OAAO,KAAK,iBAAiB,CAAC,GAAG;AAClE,UAAI,MAAuC;AACvC,eAAO,MAAM,wBAAwB,iBAAiB,iBAAsB;AAAA,MAChF;AACA,aAAO;AAAA,IACX;AACA,QAAI,MAAuC;AACvC,aAAO,IAAI,wBAAwB,iBAAiB,mFAE1B;AAAA,IAC9B;AACA,WAAO;AAAA,EACX;AACJ;;;AC1FA,SAAS,gBAAgB,SAAS;AAC9B,QAAM,gBAAgB,yBAAyB;AAC/C,gBAAc,gBAAgB,OAAO;AACzC;;;ACAA,SAAS,kBAAkB,SAAS;AAChC,QAAM,gBAAgB,yBAAyB;AAC/C,gBAAc,kBAAkB,OAAO;AAC3C;", "names": []}