{"version": 3, "sources": ["../../workbox-precaching/_version.js", "../../workbox-precaching/utils/createCacheKey.js", "../../workbox-precaching/utils/PrecacheInstallReportPlugin.js", "../../workbox-precaching/utils/PrecacheCacheKeyPlugin.js", "../../workbox-precaching/utils/printCleanupDetails.js", "../../workbox-precaching/utils/printInstallDetails.js", "../../workbox-precaching/PrecacheStrategy.js", "../../workbox-precaching/PrecacheController.js", "../../workbox-precaching/utils/getOrCreatePrecacheController.js", "../../workbox-precaching/addPlugins.js", "../../workbox-precaching/utils/removeIgnoredSearchParams.js", "../../workbox-precaching/utils/generateURLVariations.js", "../../workbox-precaching/PrecacheRoute.js", "../../workbox-precaching/addRoute.js", "../../workbox-precaching/utils/deleteOutdatedCaches.js", "../../workbox-precaching/cleanupOutdatedCaches.js", "../../workbox-precaching/createHandlerBoundToURL.js", "../../workbox-precaching/getCacheKeyForURL.js", "../../workbox-precaching/matchPrecache.js", "../../workbox-precaching/precache.js", "../../workbox-precaching/precacheAndRoute.js", "../../workbox-precaching/PrecacheFallbackPlugin.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:precaching:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport '../_version.js';\n// Name of the search parameter used to store revision info.\nconst REVISION_SEARCH_PARAM = '__WB_REVISION__';\n/**\n * Converts a manifest entry into a versioned URL suitable for precaching.\n *\n * @param {Object|string} entry\n * @return {string} A URL with versioning info.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function createCacheKey(entry) {\n    if (!entry) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If a precache manifest entry is a string, it's assumed to be a versioned\n    // URL, like '/app.abcd1234.js'. Return as-is.\n    if (typeof entry === 'string') {\n        const urlObject = new URL(entry, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    const { revision, url } = entry;\n    if (!url) {\n        throw new WorkboxError('add-to-cache-list-unexpected-type', { entry });\n    }\n    // If there's just a URL and no revision, then it's also assumed to be a\n    // versioned URL.\n    if (!revision) {\n        const urlObject = new URL(url, location.href);\n        return {\n            cacheKey: urlObject.href,\n            url: urlObject.href,\n        };\n    }\n    // Otherwise, construct a properly versioned URL using the custom Workbox\n    // search parameter along with the revision info.\n    const cacheKeyURL = new URL(url, location.href);\n    const originalURL = new URL(url, location.href);\n    cacheKeyURL.searchParams.set(REVISION_SEARCH_PARAM, revision);\n    return {\n        cacheKey: cacheKeyURL.href,\n        url: originalURL.href,\n    };\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to determine the\n * of assets that were updated (or not updated) during the install event.\n *\n * @private\n */\nclass PrecacheInstallReportPlugin {\n    constructor() {\n        this.updatedURLs = [];\n        this.notUpdatedURLs = [];\n        this.handlerWillStart = async ({ request, state, }) => {\n            // TODO: `state` should never be undefined...\n            if (state) {\n                state.originalRequest = request;\n            }\n        };\n        this.cachedResponseWillBeUsed = async ({ event, state, cachedResponse, }) => {\n            if (event.type === 'install') {\n                if (state &&\n                    state.originalRequest &&\n                    state.originalRequest instanceof Request) {\n                    // TODO: `state` should never be undefined...\n                    const url = state.originalRequest.url;\n                    if (cachedResponse) {\n                        this.notUpdatedURLs.push(url);\n                    }\n                    else {\n                        this.updatedURLs.push(url);\n                    }\n                }\n            }\n            return cachedResponse;\n        };\n    }\n}\nexport { PrecacheInstallReportPlugin };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A plugin, designed to be used with PrecacheController, to translate URLs into\n * the corresponding cache key, based on the current revision info.\n *\n * @private\n */\nclass PrecacheCacheKeyPlugin {\n    constructor({ precacheController }) {\n        this.cacheKeyWillBeUsed = async ({ request, params, }) => {\n            // Params is type any, can't change right now.\n            /* eslint-disable */\n            const cacheKey = (params === null || params === void 0 ? void 0 : params.cacheKey) ||\n                this._precacheController.getCacheKeyForURL(request.url);\n            /* eslint-enable */\n            return cacheKey\n                ? new Request(cacheKey, { headers: request.headers })\n                : request;\n        };\n        this._precacheController = precacheController;\n    }\n}\nexport { PrecacheCacheKeyPlugin };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} deletedURLs\n *\n * @private\n */\nconst logGroup = (groupTitle, deletedURLs) => {\n    logger.groupCollapsed(groupTitle);\n    for (const url of deletedURLs) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n};\n/**\n * @param {Array<string>} deletedURLs\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printCleanupDetails(deletedURLs) {\n    const deletionCount = deletedURLs.length;\n    if (deletionCount > 0) {\n        logger.groupCollapsed(`During precaching cleanup, ` +\n            `${deletionCount} cached ` +\n            `request${deletionCount === 1 ? ' was' : 's were'} deleted.`);\n        logGroup('Deleted Cache Requests', deletedURLs);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport '../_version.js';\n/**\n * @param {string} groupTitle\n * @param {Array<string>} urls\n *\n * @private\n */\nfunction _nestedGroup(groupTitle, urls) {\n    if (urls.length === 0) {\n        return;\n    }\n    logger.groupCollapsed(groupTitle);\n    for (const url of urls) {\n        logger.log(url);\n    }\n    logger.groupEnd();\n}\n/**\n * @param {Array<string>} urlsToPrecache\n * @param {Array<string>} urlsAlreadyPrecached\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function printInstallDetails(urlsToPrecache, urlsAlreadyPrecached) {\n    const precachedCount = urlsToPrecache.length;\n    const alreadyPrecachedCount = urlsAlreadyPrecached.length;\n    if (precachedCount || alreadyPrecachedCount) {\n        let message = `Precaching ${precachedCount} file${precachedCount === 1 ? '' : 's'}.`;\n        if (alreadyPrecachedCount > 0) {\n            message +=\n                ` ${alreadyPrecachedCount} ` +\n                    `file${alreadyPrecachedCount === 1 ? ' is' : 's are'} already cached.`;\n        }\n        logger.groupCollapsed(message);\n        _nestedGroup(`View newly precached URLs.`, urlsToPrecache);\n        _nestedGroup(`View previously precached URLs.`, urlsAlreadyPrecached);\n        logger.groupEnd();\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { copyResponse } from 'workbox-core/copyResponse.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Strategy } from 'workbox-strategies/Strategy.js';\nimport './_version.js';\n/**\n * A {@link workbox-strategies.Strategy} implementation\n * specifically designed to work with\n * {@link workbox-precaching.PrecacheController}\n * to both cache and fetch precached assets.\n *\n * Note: an instance of this class is created automatically when creating a\n * `PrecacheController`; it's generally not necessary to create this yourself.\n *\n * @extends workbox-strategies.Strategy\n * @memberof workbox-precaching\n */\nclass PrecacheStrategy extends Strategy {\n    /**\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] {@link https://developers.google.com/web/tools/workbox/guides/using-plugins|Plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * {@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters|init}\n     * of all fetch() requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * {@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions|CacheQueryOptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor(options = {}) {\n        options.cacheName = cacheNames.getPrecacheName(options.cacheName);\n        super(options);\n        this._fallbackToNetwork =\n            options.fallbackToNetwork === false ? false : true;\n        // Redirected responses cannot be used to satisfy a navigation request, so\n        // any redirected response must be \"copied\" rather than cloned, so the new\n        // response doesn't contain the `redirected` flag. See:\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=669363&desc=2#c1\n        this.plugins.push(PrecacheStrategy.copyRedirectedCacheableResponsesPlugin);\n    }\n    /**\n     * @private\n     * @param {Request|string} request A request to run this strategy for.\n     * @param {workbox-strategies.StrategyHandler} handler The event that\n     *     triggered the request.\n     * @return {Promise<Response>}\n     */\n    async _handle(request, handler) {\n        const response = await handler.cacheMatch(request);\n        if (response) {\n            return response;\n        }\n        // If this is an `install` event for an entry that isn't already cached,\n        // then populate the cache.\n        if (handler.event && handler.event.type === 'install') {\n            return await this._handleInstall(request, handler);\n        }\n        // Getting here means something went wrong. An entry that should have been\n        // precached wasn't found in the cache.\n        return await this._handleFetch(request, handler);\n    }\n    async _handleFetch(request, handler) {\n        let response;\n        const params = (handler.params || {});\n        // Fall back to the network if we're configured to do so.\n        if (this._fallbackToNetwork) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.warn(`The precached response for ` +\n                    `${getFriendlyURL(request.url)} in ${this.cacheName} was not ` +\n                    `found. Falling back to the network.`);\n            }\n            const integrityInManifest = params.integrity;\n            const integrityInRequest = request.integrity;\n            const noIntegrityConflict = !integrityInRequest || integrityInRequest === integrityInManifest;\n            // Do not add integrity if the original request is no-cors\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            response = await handler.fetch(new Request(request, {\n                integrity: request.mode !== 'no-cors'\n                    ? integrityInRequest || integrityInManifest\n                    : undefined,\n            }));\n            // It's only \"safe\" to repair the cache if we're using SRI to guarantee\n            // that the response matches the precache manifest's expectations,\n            // and there's either a) no integrity property in the incoming request\n            // or b) there is an integrity, and it matches the precache manifest.\n            // See https://github.com/GoogleChrome/workbox/issues/2858\n            // Also if the original request users no-cors we don't use integrity.\n            // See https://github.com/GoogleChrome/workbox/issues/3096\n            if (integrityInManifest &&\n                noIntegrityConflict &&\n                request.mode !== 'no-cors') {\n                this._useDefaultCacheabilityPluginIfNeeded();\n                const wasCached = await handler.cachePut(request, response.clone());\n                if (process.env.NODE_ENV !== 'production') {\n                    if (wasCached) {\n                        logger.log(`A response for ${getFriendlyURL(request.url)} ` +\n                            `was used to \"repair\" the precache.`);\n                    }\n                }\n            }\n        }\n        else {\n            // This shouldn't normally happen, but there are edge cases:\n            // https://github.com/GoogleChrome/workbox/issues/1441\n            throw new WorkboxError('missing-precache-entry', {\n                cacheName: this.cacheName,\n                url: request.url,\n            });\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            const cacheKey = params.cacheKey || (await handler.getCacheKey(request, 'read'));\n            // Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Precaching is responding to: ` + getFriendlyURL(request.url));\n            logger.log(`Serving the precached url: ${getFriendlyURL(cacheKey instanceof Request ? cacheKey.url : cacheKey)}`);\n            logger.groupCollapsed(`View request details here.`);\n            logger.log(request);\n            logger.groupEnd();\n            logger.groupCollapsed(`View response details here.`);\n            logger.log(response);\n            logger.groupEnd();\n            logger.groupEnd();\n        }\n        return response;\n    }\n    async _handleInstall(request, handler) {\n        this._useDefaultCacheabilityPluginIfNeeded();\n        const response = await handler.fetch(request);\n        // Make sure we defer cachePut() until after we know the response\n        // should be cached; see https://github.com/GoogleChrome/workbox/issues/2737\n        const wasCached = await handler.cachePut(request, response.clone());\n        if (!wasCached) {\n            // Throwing here will lead to the `install` handler failing, which\n            // we want to do if *any* of the responses aren't safe to cache.\n            throw new WorkboxError('bad-precaching-response', {\n                url: request.url,\n                status: response.status,\n            });\n        }\n        return response;\n    }\n    /**\n     * This method is complex, as there a number of things to account for:\n     *\n     * The `plugins` array can be set at construction, and/or it might be added to\n     * to at any time before the strategy is used.\n     *\n     * At the time the strategy is used (i.e. during an `install` event), there\n     * needs to be at least one plugin that implements `cacheWillUpdate` in the\n     * array, other than `copyRedirectedCacheableResponsesPlugin`.\n     *\n     * - If this method is called and there are no suitable `cacheWillUpdate`\n     * plugins, we need to add `defaultPrecacheCacheabilityPlugin`.\n     *\n     * - If this method is called and there is exactly one `cacheWillUpdate`, then\n     * we don't have to do anything (this might be a previously added\n     * `defaultPrecacheCacheabilityPlugin`, or it might be a custom plugin).\n     *\n     * - If this method is called and there is more than one `cacheWillUpdate`,\n     * then we need to check if one is `defaultPrecacheCacheabilityPlugin`. If so,\n     * we need to remove it. (This situation is unlikely, but it could happen if\n     * the strategy is used multiple times, the first without a `cacheWillUpdate`,\n     * and then later on after manually adding a custom `cacheWillUpdate`.)\n     *\n     * See https://github.com/GoogleChrome/workbox/issues/2737 for more context.\n     *\n     * @private\n     */\n    _useDefaultCacheabilityPluginIfNeeded() {\n        let defaultPluginIndex = null;\n        let cacheWillUpdatePluginCount = 0;\n        for (const [index, plugin] of this.plugins.entries()) {\n            // Ignore the copy redirected plugin when determining what to do.\n            if (plugin === PrecacheStrategy.copyRedirectedCacheableResponsesPlugin) {\n                continue;\n            }\n            // Save the default plugin's index, in case it needs to be removed.\n            if (plugin === PrecacheStrategy.defaultPrecacheCacheabilityPlugin) {\n                defaultPluginIndex = index;\n            }\n            if (plugin.cacheWillUpdate) {\n                cacheWillUpdatePluginCount++;\n            }\n        }\n        if (cacheWillUpdatePluginCount === 0) {\n            this.plugins.push(PrecacheStrategy.defaultPrecacheCacheabilityPlugin);\n        }\n        else if (cacheWillUpdatePluginCount > 1 && defaultPluginIndex !== null) {\n            // Only remove the default plugin; multiple custom plugins are allowed.\n            this.plugins.splice(defaultPluginIndex, 1);\n        }\n        // Nothing needs to be done if cacheWillUpdatePluginCount is 1\n    }\n}\nPrecacheStrategy.defaultPrecacheCacheabilityPlugin = {\n    async cacheWillUpdate({ response }) {\n        if (!response || response.status >= 400) {\n            return null;\n        }\n        return response;\n    },\n};\nPrecacheStrategy.copyRedirectedCacheableResponsesPlugin = {\n    async cacheWillUpdate({ response }) {\n        return response.redirected ? await copyResponse(response) : response;\n    },\n};\nexport { PrecacheStrategy };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { waitUntil } from 'workbox-core/_private/waitUntil.js';\nimport { createCacheKey } from './utils/createCacheKey.js';\nimport { PrecacheInstallReportPlugin } from './utils/PrecacheInstallReportPlugin.js';\nimport { PrecacheCacheKeyPlugin } from './utils/PrecacheCacheKeyPlugin.js';\nimport { printCleanupDetails } from './utils/printCleanupDetails.js';\nimport { printInstallDetails } from './utils/printInstallDetails.js';\nimport { PrecacheStrategy } from './PrecacheStrategy.js';\nimport './_version.js';\n/**\n * Performs efficient precaching of assets.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheController {\n    /**\n     * Create a new PrecacheController.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] The cache to use for precaching.\n     * @param {string} [options.plugins] Plugins to use when precaching as well\n     * as responding to fetch events for precached assets.\n     * @param {boolean} [options.fallbackToNetwork=true] Whether to attempt to\n     * get the response from the network if there's a precache miss.\n     */\n    constructor({ cacheName, plugins = [], fallbackToNetwork = true, } = {}) {\n        this._urlsToCacheKeys = new Map();\n        this._urlsToCacheModes = new Map();\n        this._cacheKeysToIntegrities = new Map();\n        this._strategy = new PrecacheStrategy({\n            cacheName: cacheNames.getPrecacheName(cacheName),\n            plugins: [\n                ...plugins,\n                new PrecacheCacheKeyPlugin({ precacheController: this }),\n            ],\n            fallbackToNetwork,\n        });\n        // Bind the install and activate methods to the instance.\n        this.install = this.install.bind(this);\n        this.activate = this.activate.bind(this);\n    }\n    /**\n     * @type {workbox-precaching.PrecacheStrategy} The strategy created by this controller and\n     * used to cache assets and respond to fetch events.\n     */\n    get strategy() {\n        return this._strategy;\n    }\n    /**\n     * Adds items to the precache list, removing any duplicates and\n     * stores the files in the\n     * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n     * worker installs.\n     *\n     * This method can be called multiple times.\n     *\n     * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n     */\n    precache(entries) {\n        this.addToCacheList(entries);\n        if (!this._installAndActiveListenersAdded) {\n            self.addEventListener('install', this.install);\n            self.addEventListener('activate', this.activate);\n            this._installAndActiveListenersAdded = true;\n        }\n    }\n    /**\n     * This method will add items to the precache list, removing duplicates\n     * and ensuring the information is valid.\n     *\n     * @param {Array<workbox-precaching.PrecacheController.PrecacheEntry|string>} entries\n     *     Array of entries to precache.\n     */\n    addToCacheList(entries) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isArray(entries, {\n                moduleName: 'workbox-precaching',\n                className: 'PrecacheController',\n                funcName: 'addToCacheList',\n                paramName: 'entries',\n            });\n        }\n        const urlsToWarnAbout = [];\n        for (const entry of entries) {\n            // See https://github.com/GoogleChrome/workbox/issues/2259\n            if (typeof entry === 'string') {\n                urlsToWarnAbout.push(entry);\n            }\n            else if (entry && entry.revision === undefined) {\n                urlsToWarnAbout.push(entry.url);\n            }\n            const { cacheKey, url } = createCacheKey(entry);\n            const cacheMode = typeof entry !== 'string' && entry.revision ? 'reload' : 'default';\n            if (this._urlsToCacheKeys.has(url) &&\n                this._urlsToCacheKeys.get(url) !== cacheKey) {\n                throw new WorkboxError('add-to-cache-list-conflicting-entries', {\n                    firstEntry: this._urlsToCacheKeys.get(url),\n                    secondEntry: cacheKey,\n                });\n            }\n            if (typeof entry !== 'string' && entry.integrity) {\n                if (this._cacheKeysToIntegrities.has(cacheKey) &&\n                    this._cacheKeysToIntegrities.get(cacheKey) !== entry.integrity) {\n                    throw new WorkboxError('add-to-cache-list-conflicting-integrities', {\n                        url,\n                    });\n                }\n                this._cacheKeysToIntegrities.set(cacheKey, entry.integrity);\n            }\n            this._urlsToCacheKeys.set(url, cacheKey);\n            this._urlsToCacheModes.set(url, cacheMode);\n            if (urlsToWarnAbout.length > 0) {\n                const warningMessage = `Workbox is precaching URLs without revision ` +\n                    `info: ${urlsToWarnAbout.join(', ')}\\nThis is generally NOT safe. ` +\n                    `Learn more at https://bit.ly/wb-precache`;\n                if (process.env.NODE_ENV === 'production') {\n                    // Use console directly to display this warning without bloating\n                    // bundle sizes by pulling in all of the logger codebase in prod.\n                    console.warn(warningMessage);\n                }\n                else {\n                    logger.warn(warningMessage);\n                }\n            }\n        }\n    }\n    /**\n     * Precaches new and updated assets. Call this method from the service worker\n     * install event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.InstallResult>}\n     */\n    install(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const installReportPlugin = new PrecacheInstallReportPlugin();\n            this.strategy.plugins.push(installReportPlugin);\n            // Cache entries one at a time.\n            // See https://github.com/GoogleChrome/workbox/issues/2528\n            for (const [url, cacheKey] of this._urlsToCacheKeys) {\n                const integrity = this._cacheKeysToIntegrities.get(cacheKey);\n                const cacheMode = this._urlsToCacheModes.get(url);\n                const request = new Request(url, {\n                    integrity,\n                    cache: cacheMode,\n                    credentials: 'same-origin',\n                });\n                await Promise.all(this.strategy.handleAll({\n                    params: { cacheKey },\n                    request,\n                    event,\n                }));\n            }\n            const { updatedURLs, notUpdatedURLs } = installReportPlugin;\n            if (process.env.NODE_ENV !== 'production') {\n                printInstallDetails(updatedURLs, notUpdatedURLs);\n            }\n            return { updatedURLs, notUpdatedURLs };\n        });\n    }\n    /**\n     * Deletes assets that are no longer present in the current precache manifest.\n     * Call this method from the service worker activate event.\n     *\n     * Note: this method calls `event.waitUntil()` for you, so you do not need\n     * to call it yourself in your event handlers.\n     *\n     * @param {ExtendableEvent} event\n     * @return {Promise<workbox-precaching.CleanupResult>}\n     */\n    activate(event) {\n        // waitUntil returns Promise<any>\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-return\n        return waitUntil(event, async () => {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            const currentlyCachedRequests = await cache.keys();\n            const expectedCacheKeys = new Set(this._urlsToCacheKeys.values());\n            const deletedURLs = [];\n            for (const request of currentlyCachedRequests) {\n                if (!expectedCacheKeys.has(request.url)) {\n                    await cache.delete(request);\n                    deletedURLs.push(request.url);\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                printCleanupDetails(deletedURLs);\n            }\n            return { deletedURLs };\n        });\n    }\n    /**\n     * Returns a mapping of a precached URL to the corresponding cache key, taking\n     * into account the revision information for the URL.\n     *\n     * @return {Map<string, string>} A URL to cache key mapping.\n     */\n    getURLsToCacheKeys() {\n        return this._urlsToCacheKeys;\n    }\n    /**\n     * Returns a list of all the URLs that have been precached by the current\n     * service worker.\n     *\n     * @return {Array<string>} The precached URLs.\n     */\n    getCachedURLs() {\n        return [...this._urlsToCacheKeys.keys()];\n    }\n    /**\n     * Returns the cache key used for storing a given URL. If that URL is\n     * unversioned, like `/index.html', then the cache key will be the original\n     * URL with a search parameter appended to it.\n     *\n     * @param {string} url A URL whose cache key you want to look up.\n     * @return {string} The versioned URL that corresponds to a cache key\n     * for the original URL, or undefined if that URL isn't precached.\n     */\n    getCacheKeyForURL(url) {\n        const urlObject = new URL(url, location.href);\n        return this._urlsToCacheKeys.get(urlObject.href);\n    }\n    /**\n     * @param {string} url A cache key whose SRI you want to look up.\n     * @return {string} The subresource integrity associated with the cache key,\n     * or undefined if it's not set.\n     */\n    getIntegrityForCacheKey(cacheKey) {\n        return this._cacheKeysToIntegrities.get(cacheKey);\n    }\n    /**\n     * This acts as a drop-in replacement for\n     * [`cache.match()`](https://developer.mozilla.org/en-US/docs/Web/API/Cache/match)\n     * with the following differences:\n     *\n     * - It knows what the name of the precache is, and only checks in that cache.\n     * - It allows you to pass in an \"original\" URL without versioning parameters,\n     * and it will automatically look up the correct cache key for the currently\n     * active revision of that URL.\n     *\n     * E.g., `matchPrecache('index.html')` will find the correct precached\n     * response for the currently active service worker, even if the actual cache\n     * key is `'/index.html?__WB_REVISION__=1234abcd'`.\n     *\n     * @param {string|Request} request The key (without revisioning parameters)\n     * to look up in the precache.\n     * @return {Promise<Response|undefined>}\n     */\n    async matchPrecache(request) {\n        const url = request instanceof Request ? request.url : request;\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (cacheKey) {\n            const cache = await self.caches.open(this.strategy.cacheName);\n            return cache.match(cacheKey);\n        }\n        return undefined;\n    }\n    /**\n     * Returns a function that looks up `url` in the precache (taking into\n     * account revision information), and returns the corresponding `Response`.\n     *\n     * @param {string} url The precached URL which will be used to lookup the\n     * `Response`.\n     * @return {workbox-routing~handlerCallback}\n     */\n    createHandlerBoundToURL(url) {\n        const cacheKey = this.getCacheKeyForURL(url);\n        if (!cacheKey) {\n            throw new WorkboxError('non-precached-url', { url });\n        }\n        return (options) => {\n            options.request = new Request(url);\n            options.params = Object.assign({ cacheKey }, options.params);\n            return this.strategy.handle(options);\n        };\n    }\n}\nexport { PrecacheController };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { PrecacheController } from '../PrecacheController.js';\nimport '../_version.js';\nlet precacheController;\n/**\n * @return {PrecacheController}\n * @private\n */\nexport const getOrCreatePrecacheController = () => {\n    if (!precacheController) {\n        precacheController = new PrecacheController();\n    }\n    return precacheController;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds plugins to the precaching strategy.\n *\n * @param {Array<Object>} plugins\n *\n * @memberof workbox-precaching\n */\nfunction addPlugins(plugins) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.strategy.plugins.push(...plugins);\n}\nexport { addPlugins };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Removes any URL search parameters that should be ignored.\n *\n * @param {URL} urlObject The original URL.\n * @param {Array<RegExp>} ignoreURLParametersMatching RegExps to test against\n * each search parameter name. Matches mean that the search parameter should be\n * ignored.\n * @return {URL} The URL with any ignored search parameters removed.\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching = []) {\n    // Convert the iterable into an array at the start of the loop to make sure\n    // deletion doesn't mess up iteration.\n    for (const paramName of [...urlObject.searchParams.keys()]) {\n        if (ignoreURLParametersMatching.some((regExp) => regExp.test(paramName))) {\n            urlObject.searchParams.delete(paramName);\n        }\n    }\n    return urlObject;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { removeIgnoredSearchParams } from './removeIgnoredSearchParams.js';\nimport '../_version.js';\n/**\n * Generator function that yields possible variations on the original URL to\n * check, one at a time.\n *\n * @param {string} url\n * @param {Object} options\n *\n * @private\n * @memberof workbox-precaching\n */\nexport function* generateURLVariations(url, { ignoreURLParametersMatching = [/^utm_/, /^fbclid$/], directoryIndex = 'index.html', cleanURLs = true, urlManipulation, } = {}) {\n    const urlObject = new URL(url, location.href);\n    urlObject.hash = '';\n    yield urlObject.href;\n    const urlWithoutIgnoredParams = removeIgnoredSearchParams(urlObject, ignoreURLParametersMatching);\n    yield urlWithoutIgnoredParams.href;\n    if (directoryIndex && urlWithoutIgnoredParams.pathname.endsWith('/')) {\n        const directoryURL = new URL(urlWithoutIgnoredParams.href);\n        directoryURL.pathname += directoryIndex;\n        yield directoryURL.href;\n    }\n    if (cleanURLs) {\n        const cleanURL = new URL(urlWithoutIgnoredParams.href);\n        cleanURL.pathname += '.html';\n        yield cleanURL.href;\n    }\n    if (urlManipulation) {\n        const additionalURLs = urlManipulation({ url: urlObject });\n        for (const urlToAttempt of additionalURLs) {\n            yield urlToAttempt.href;\n        }\n    }\n}\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { Route } from 'workbox-routing/Route.js';\nimport { generateURLVariations } from './utils/generateURLVariations.js';\nimport './_version.js';\n/**\n * A subclass of {@link workbox-routing.Route} that takes a\n * {@link workbox-precaching.PrecacheController}\n * instance and uses it to match incoming requests and handle fetching\n * responses from the precache.\n *\n * @memberof workbox-precaching\n * @extends workbox-routing.Route\n */\nclass PrecacheRoute extends Route {\n    /**\n     * @param {PrecacheController} precacheController A `PrecacheController`\n     * instance used to both match requests and respond to fetch events.\n     * @param {Object} [options] Options to control how requests are matched\n     * against the list of precached URLs.\n     * @param {string} [options.directoryIndex=index.html] The `directoryIndex` will\n     * check cache entries for a URLs ending with '/' to see if there is a hit when\n     * appending the `directoryIndex` value.\n     * @param {Array<RegExp>} [options.ignoreURLParametersMatching=[/^utm_/, /^fbclid$/]] An\n     * array of regex's to remove search params when looking for a cache match.\n     * @param {boolean} [options.cleanURLs=true] The `cleanURLs` option will\n     * check the cache for the URL with a `.html` added to the end of the end.\n     * @param {workbox-precaching~urlManipulation} [options.urlManipulation]\n     * This is a function that should take a URL and return an array of\n     * alternative URLs that should be checked for precache matches.\n     */\n    constructor(precacheController, options) {\n        const match = ({ request, }) => {\n            const urlsToCacheKeys = precacheController.getURLsToCacheKeys();\n            for (const possibleURL of generateURLVariations(request.url, options)) {\n                const cacheKey = urlsToCacheKeys.get(possibleURL);\n                if (cacheKey) {\n                    const integrity = precacheController.getIntegrityForCacheKey(cacheKey);\n                    return { cacheKey, integrity };\n                }\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Precaching did not find a match for ` + getFriendlyURL(request.url));\n            }\n            return;\n        };\n        super(match, precacheController.strategy);\n    }\n}\nexport { PrecacheRoute };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { registerRoute } from 'workbox-routing/registerRoute.js';\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport { PrecacheRoute } from './PrecacheRoute.js';\nimport './_version.js';\n/**\n * Add a `fetch` listener to the service worker that will\n * respond to\n * [network requests]{@link https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API/Using_Service_Workers#Custom_responses_to_requests}\n * with precached assets.\n *\n * Requests for assets that aren't precached, the `FetchEvent` will not be\n * responded to, allowing the event to fall through to other `fetch` event\n * listeners.\n *\n * @param {Object} [options] See the {@link workbox-precaching.PrecacheRoute}\n * options.\n *\n * @memberof workbox-precaching\n */\nfunction addRoute(options) {\n    const precacheController = getOrCreatePrecacheController();\n    const precacheRoute = new PrecacheRoute(precacheController, options);\n    registerRoute(precacheRoute);\n}\nexport { addRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst SUBSTRING_TO_FIND = '-precache-';\n/**\n * Cleans up incompatible precaches that were created by older versions of\n * Workbox, by a service worker registered under the current scope.\n *\n * This is meant to be called as part of the `activate` event.\n *\n * This should be safe to use as long as you don't include `substringToFind`\n * (defaulting to `-precache-`) in your non-precache cache names.\n *\n * @param {string} currentPrecacheName The cache name currently in use for\n * precaching. This cache won't be deleted.\n * @param {string} [substringToFind='-precache-'] Cache names which include this\n * substring will be deleted (excluding `currentPrecacheName`).\n * @return {Array<string>} A list of all the cache names that were deleted.\n *\n * @private\n * @memberof workbox-precaching\n */\nconst deleteOutdatedCaches = async (currentPrecacheName, substringToFind = SUBSTRING_TO_FIND) => {\n    const cacheNames = await self.caches.keys();\n    const cacheNamesToDelete = cacheNames.filter((cacheName) => {\n        return (cacheName.includes(substringToFind) &&\n            cacheName.includes(self.registration.scope) &&\n            cacheName !== currentPrecacheName);\n    });\n    await Promise.all(cacheNamesToDelete.map((cacheName) => self.caches.delete(cacheName)));\n    return cacheNamesToDelete;\n};\nexport { deleteOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { deleteOutdatedCaches } from './utils/deleteOutdatedCaches.js';\nimport './_version.js';\n/**\n * Adds an `activate` event listener which will clean up incompatible\n * precaches that were created by older versions of Workbox.\n *\n * @memberof workbox-precaching\n */\nfunction cleanupOutdatedCaches() {\n    // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-436484705\n    self.addEventListener('activate', ((event) => {\n        const cacheName = cacheNames.getPrecacheName();\n        event.waitUntil(deleteOutdatedCaches(cacheName).then((cachesDeleted) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (cachesDeleted.length > 0) {\n                    logger.log(`The following out-of-date precaches were cleaned up ` +\n                        `automatically:`, cachesDeleted);\n                }\n            }\n        }));\n    }));\n}\nexport { cleanupOutdatedCaches };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#createHandlerBoundToURL} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call the\n * {@link PrecacheController#createHandlerBoundToURL} on that instance,\n * instead of using this function.\n *\n * @param {string} url The precached URL which will be used to lookup the\n * `Response`.\n * @param {boolean} [fallbackToNetwork=true] Whether to attempt to get the\n * response from the network if there's a precache miss.\n * @return {workbox-routing~handlerCallback}\n *\n * @memberof workbox-precaching\n */\nfunction createHandlerBoundToURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.createHandlerBoundToURL(url);\n}\nexport { createHandlerBoundToURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Takes in a URL, and returns the corresponding URL that could be used to\n * lookup the entry in the precache.\n *\n * If a relative URL is provided, the location of the service worker file will\n * be used as the base.\n *\n * For precached entries without revision information, the cache key will be the\n * same as the original URL.\n *\n * For precached entries with revision information, the cache key will be the\n * original URL with the addition of a query parameter used for keeping track of\n * the revision info.\n *\n * @param {string} url The URL whose cache key to look up.\n * @return {string} The cache key that corresponds to that URL.\n *\n * @memberof workbox-precaching\n */\nfunction getCacheKeyForURL(url) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.getCacheKeyForURL(url);\n}\nexport { getCacheKeyForURL };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Helper function that calls\n * {@link PrecacheController#matchPrecache} on the default\n * {@link PrecacheController} instance.\n *\n * If you are creating your own {@link PrecacheController}, then call\n * {@link PrecacheController#matchPrecache} on that instance,\n * instead of using this function.\n *\n * @param {string|Request} request The key (without revisioning parameters)\n * to look up in the precache.\n * @return {Promise<Response|undefined>}\n *\n * @memberof workbox-precaching\n */\nfunction matchPrecache(request) {\n    const precacheController = getOrCreatePrecacheController();\n    return precacheController.matchPrecache(request);\n}\nexport { matchPrecache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * Adds items to the precache list, removing any duplicates and\n * stores the files in the\n * {@link workbox-core.cacheNames|\"precache cache\"} when the service\n * worker installs.\n *\n * This method can be called multiple times.\n *\n * Please note: This method **will not** serve any of the cached files for you.\n * It only precaches files. To respond to a network request you call\n * {@link workbox-precaching.addRoute}.\n *\n * If you have a single array of files to precache, you can just call\n * {@link workbox-precaching.precacheAndRoute}.\n *\n * @param {Array<Object|string>} [entries=[]] Array of entries to precache.\n *\n * @memberof workbox-precaching\n */\nfunction precache(entries) {\n    const precacheController = getOrCreatePrecacheController();\n    precacheController.precache(entries);\n}\nexport { precache };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { addRoute } from './addRoute.js';\nimport { precache } from './precache.js';\nimport './_version.js';\n/**\n * This method will add entries to the precache list and add a route to\n * respond to fetch events.\n *\n * This is a convenience method that will call\n * {@link workbox-precaching.precache} and\n * {@link workbox-precaching.addRoute} in a single call.\n *\n * @param {Array<Object|string>} entries Array of entries to precache.\n * @param {Object} [options] See the\n * {@link workbox-precaching.PrecacheRoute} options.\n *\n * @memberof workbox-precaching\n */\nfunction precacheAndRoute(entries, options) {\n    precache(entries);\n    addRoute(options);\n}\nexport { precacheAndRoute };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { getOrCreatePrecacheController } from './utils/getOrCreatePrecacheController.js';\nimport './_version.js';\n/**\n * `PrecacheFallbackPlugin` allows you to specify an \"offline fallback\"\n * response to be used when a given strategy is unable to generate a response.\n *\n * It does this by intercepting the `handlerDidError` plugin callback\n * and returning a precached response, taking the expected revision parameter\n * into account automatically.\n *\n * Unless you explicitly pass in a `PrecacheController` instance to the\n * constructor, the default instance will be used. Generally speaking, most\n * developers will end up using the default.\n *\n * @memberof workbox-precaching\n */\nclass PrecacheFallbackPlugin {\n    /**\n     * Constructs a new PrecacheFallbackPlugin with the associated fallbackURL.\n     *\n     * @param {Object} config\n     * @param {string} config.fallbackURL A precached URL to use as the fallback\n     *     if the associated strategy can't generate a response.\n     * @param {PrecacheController} [config.precacheController] An optional\n     *     PrecacheController instance. If not provided, the default\n     *     PrecacheController will be used.\n     */\n    constructor({ fallbackURL, precacheController, }) {\n        /**\n         * @return {Promise<Response>} The precache response for the fallback URL.\n         *\n         * @private\n         */\n        this.handlerDidError = () => this._precacheController.matchPrecache(this._fallbackURL);\n        this._fallbackURL = fallbackURL;\n        this._precacheController =\n            precacheController || getOrCreatePrecacheController();\n    }\n}\nexport { PrecacheFallbackPlugin };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI;AACA,OAAK,0BAA0B,KAAK,EAAE;AAC1C,SACO,GAAG;AAAE;;;ACKZ,IAAM,wBAAwB;AAUvB,SAAS,eAAe,OAAO;AAClC,MAAI,CAAC,OAAO;AACR,UAAM,IAAI,aAAa,qCAAqC,EAAE,MAAM,CAAC;AAAA,EACzE;AAGA,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,YAAY,IAAI,IAAI,OAAO,SAAS,IAAI;AAC9C,WAAO;AAAA,MACH,UAAU,UAAU;AAAA,MACpB,KAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AACA,QAAM,EAAE,UAAU,IAAI,IAAI;AAC1B,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,aAAa,qCAAqC,EAAE,MAAM,CAAC;AAAA,EACzE;AAGA,MAAI,CAAC,UAAU;AACX,UAAM,YAAY,IAAI,IAAI,KAAK,SAAS,IAAI;AAC5C,WAAO;AAAA,MACH,UAAU,UAAU;AAAA,MACpB,KAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAGA,QAAM,cAAc,IAAI,IAAI,KAAK,SAAS,IAAI;AAC9C,QAAM,cAAc,IAAI,IAAI,KAAK,SAAS,IAAI;AAC9C,cAAY,aAAa,IAAI,uBAAuB,QAAQ;AAC5D,SAAO;AAAA,IACH,UAAU,YAAY;AAAA,IACtB,KAAK,YAAY;AAAA,EACrB;AACJ;;;ACzCA,IAAM,8BAAN,MAAkC;AAAA,EAC9B,cAAc;AACV,SAAK,cAAc,CAAC;AACpB,SAAK,iBAAiB,CAAC;AACvB,SAAK,mBAAmB,OAAO,EAAE,SAAS,MAAO,MAAM;AAEnD,UAAI,OAAO;AACP,cAAM,kBAAkB;AAAA,MAC5B;AAAA,IACJ;AACA,SAAK,2BAA2B,OAAO,EAAE,OAAO,OAAO,eAAgB,MAAM;AACzE,UAAI,MAAM,SAAS,WAAW;AAC1B,YAAI,SACA,MAAM,mBACN,MAAM,2BAA2B,SAAS;AAE1C,gBAAM,MAAM,MAAM,gBAAgB;AAClC,cAAI,gBAAgB;AAChB,iBAAK,eAAe,KAAK,GAAG;AAAA,UAChC,OACK;AACD,iBAAK,YAAY,KAAK,GAAG;AAAA,UAC7B;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;;;AC5BA,IAAM,yBAAN,MAA6B;AAAA,EACzB,YAAY,EAAE,oBAAAA,oBAAmB,GAAG;AAChC,SAAK,qBAAqB,OAAO,EAAE,SAAS,OAAQ,MAAM;AAGtD,YAAM,YAAY,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,aACrE,KAAK,oBAAoB,kBAAkB,QAAQ,GAAG;AAE1D,aAAO,WACD,IAAI,QAAQ,UAAU,EAAE,SAAS,QAAQ,QAAQ,CAAC,IAClD;AAAA,IACV;AACA,SAAK,sBAAsBA;AAAA,EAC/B;AACJ;;;ACbA,IAAM,WAAW,CAAC,YAAY,gBAAgB;AAC1C,SAAO,eAAe,UAAU;AAChC,aAAW,OAAO,aAAa;AAC3B,WAAO,IAAI,GAAG;AAAA,EAClB;AACA,SAAO,SAAS;AACpB;AAOO,SAAS,oBAAoB,aAAa;AAC7C,QAAM,gBAAgB,YAAY;AAClC,MAAI,gBAAgB,GAAG;AACnB,WAAO,eAAe,8BACf,aAAa,kBACN,kBAAkB,IAAI,SAAS,QAAQ,WAAW;AAChE,aAAS,0BAA0B,WAAW;AAC9C,WAAO,SAAS;AAAA,EACpB;AACJ;;;ACtBA,SAAS,aAAa,YAAY,MAAM;AACpC,MAAI,KAAK,WAAW,GAAG;AACnB;AAAA,EACJ;AACA,SAAO,eAAe,UAAU;AAChC,aAAW,OAAO,MAAM;AACpB,WAAO,IAAI,GAAG;AAAA,EAClB;AACA,SAAO,SAAS;AACpB;AAQO,SAAS,oBAAoB,gBAAgB,sBAAsB;AACtE,QAAM,iBAAiB,eAAe;AACtC,QAAM,wBAAwB,qBAAqB;AACnD,MAAI,kBAAkB,uBAAuB;AACzC,QAAI,UAAU,cAAc,cAAc,QAAQ,mBAAmB,IAAI,KAAK,GAAG;AACjF,QAAI,wBAAwB,GAAG;AAC3B,iBACI,IAAI,qBAAqB,QACd,0BAA0B,IAAI,QAAQ,OAAO;AAAA,IAChE;AACA,WAAO,eAAe,OAAO;AAC7B,iBAAa,8BAA8B,cAAc;AACzD,iBAAa,mCAAmC,oBAAoB;AACpE,WAAO,SAAS;AAAA,EACpB;AACJ;;;ACrBA,IAAM,mBAAN,MAAM,0BAAyB,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBpC,YAAY,UAAU,CAAC,GAAG;AACtB,YAAQ,YAAY,WAAW,gBAAgB,QAAQ,SAAS;AAChE,UAAM,OAAO;AACb,SAAK,qBACD,QAAQ,sBAAsB,QAAQ,QAAQ;AAKlD,SAAK,QAAQ,KAAK,kBAAiB,sCAAsC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,QAAQ,SAAS,SAAS;AAC5B,UAAM,WAAW,MAAM,QAAQ,WAAW,OAAO;AACjD,QAAI,UAAU;AACV,aAAO;AAAA,IACX;AAGA,QAAI,QAAQ,SAAS,QAAQ,MAAM,SAAS,WAAW;AACnD,aAAO,MAAM,KAAK,eAAe,SAAS,OAAO;AAAA,IACrD;AAGA,WAAO,MAAM,KAAK,aAAa,SAAS,OAAO;AAAA,EACnD;AAAA,EACA,MAAM,aAAa,SAAS,SAAS;AACjC,QAAI;AACJ,UAAM,SAAU,QAAQ,UAAU,CAAC;AAEnC,QAAI,KAAK,oBAAoB;AACzB,UAAI,MAAuC;AACvC,eAAO,KAAK,8BACL,eAAe,QAAQ,GAAG,CAAC,OAAO,KAAK,SAAS,8CACd;AAAA,MAC7C;AACA,YAAM,sBAAsB,OAAO;AACnC,YAAM,qBAAqB,QAAQ;AACnC,YAAM,sBAAsB,CAAC,sBAAsB,uBAAuB;AAG1E,iBAAW,MAAM,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAAA,QAChD,WAAW,QAAQ,SAAS,YACtB,sBAAsB,sBACtB;AAAA,MACV,CAAC,CAAC;AAQF,UAAI,uBACA,uBACA,QAAQ,SAAS,WAAW;AAC5B,aAAK,sCAAsC;AAC3C,cAAM,YAAY,MAAM,QAAQ,SAAS,SAAS,SAAS,MAAM,CAAC;AAClE,YAAI,MAAuC;AACvC,cAAI,WAAW;AACX,mBAAO,IAAI,kBAAkB,eAAe,QAAQ,GAAG,CAAC,qCAChB;AAAA,UAC5C;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,OACK;AAGD,YAAM,IAAI,aAAa,0BAA0B;AAAA,QAC7C,WAAW,KAAK;AAAA,QAChB,KAAK,QAAQ;AAAA,MACjB,CAAC;AAAA,IACL;AACA,QAAI,MAAuC;AACvC,YAAM,WAAW,OAAO,YAAa,MAAM,QAAQ,YAAY,SAAS,MAAM;AAG9E,aAAO,eAAe,kCAAkC,eAAe,QAAQ,GAAG,CAAC;AACnF,aAAO,IAAI,8BAA8B,eAAe,oBAAoB,UAAU,SAAS,MAAM,QAAQ,CAAC,EAAE;AAChH,aAAO,eAAe,4BAA4B;AAClD,aAAO,IAAI,OAAO;AAClB,aAAO,SAAS;AAChB,aAAO,eAAe,6BAA6B;AACnD,aAAO,IAAI,QAAQ;AACnB,aAAO,SAAS;AAChB,aAAO,SAAS;AAAA,IACpB;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,eAAe,SAAS,SAAS;AACnC,SAAK,sCAAsC;AAC3C,UAAM,WAAW,MAAM,QAAQ,MAAM,OAAO;AAG5C,UAAM,YAAY,MAAM,QAAQ,SAAS,SAAS,SAAS,MAAM,CAAC;AAClE,QAAI,CAAC,WAAW;AAGZ,YAAM,IAAI,aAAa,2BAA2B;AAAA,QAC9C,KAAK,QAAQ;AAAA,QACb,QAAQ,SAAS;AAAA,MACrB,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4BA,wCAAwC;AACpC,QAAI,qBAAqB;AACzB,QAAI,6BAA6B;AACjC,eAAW,CAAC,OAAO,MAAM,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAElD,UAAI,WAAW,kBAAiB,wCAAwC;AACpE;AAAA,MACJ;AAEA,UAAI,WAAW,kBAAiB,mCAAmC;AAC/D,6BAAqB;AAAA,MACzB;AACA,UAAI,OAAO,iBAAiB;AACxB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,+BAA+B,GAAG;AAClC,WAAK,QAAQ,KAAK,kBAAiB,iCAAiC;AAAA,IACxE,WACS,6BAA6B,KAAK,uBAAuB,MAAM;AAEpE,WAAK,QAAQ,OAAO,oBAAoB,CAAC;AAAA,IAC7C;AAAA,EAEJ;AACJ;AACA,iBAAiB,oCAAoC;AAAA,EACjD,MAAM,gBAAgB,EAAE,SAAS,GAAG;AAChC,QAAI,CAAC,YAAY,SAAS,UAAU,KAAK;AACrC,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACJ;AACA,iBAAiB,yCAAyC;AAAA,EACtD,MAAM,gBAAgB,EAAE,SAAS,GAAG;AAChC,WAAO,SAAS,aAAa,MAAM,aAAa,QAAQ,IAAI;AAAA,EAChE;AACJ;;;ACrMA,IAAM,qBAAN,MAAyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWrB,YAAY,EAAE,WAAW,UAAU,CAAC,GAAG,oBAAoB,KAAM,IAAI,CAAC,GAAG;AACrE,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,oBAAoB,oBAAI,IAAI;AACjC,SAAK,0BAA0B,oBAAI,IAAI;AACvC,SAAK,YAAY,IAAI,iBAAiB;AAAA,MAClC,WAAW,WAAW,gBAAgB,SAAS;AAAA,MAC/C,SAAS;AAAA,QACL,GAAG;AAAA,QACH,IAAI,uBAAuB,EAAE,oBAAoB,KAAK,CAAC;AAAA,MAC3D;AAAA,MACA;AAAA,IACJ,CAAC;AAED,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AACrC,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,SAAS;AACd,SAAK,eAAe,OAAO;AAC3B,QAAI,CAAC,KAAK,iCAAiC;AACvC,WAAK,iBAAiB,WAAW,KAAK,OAAO;AAC7C,WAAK,iBAAiB,YAAY,KAAK,QAAQ;AAC/C,WAAK,kCAAkC;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,eAAe,SAAS;AACpB,QAAI,MAAuC;AACvC,yBAAO,QAAQ,SAAS;AAAA,QACpB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,kBAAkB,CAAC;AACzB,eAAW,SAAS,SAAS;AAEzB,UAAI,OAAO,UAAU,UAAU;AAC3B,wBAAgB,KAAK,KAAK;AAAA,MAC9B,WACS,SAAS,MAAM,aAAa,QAAW;AAC5C,wBAAgB,KAAK,MAAM,GAAG;AAAA,MAClC;AACA,YAAM,EAAE,UAAU,IAAI,IAAI,eAAe,KAAK;AAC9C,YAAM,YAAY,OAAO,UAAU,YAAY,MAAM,WAAW,WAAW;AAC3E,UAAI,KAAK,iBAAiB,IAAI,GAAG,KAC7B,KAAK,iBAAiB,IAAI,GAAG,MAAM,UAAU;AAC7C,cAAM,IAAI,aAAa,yCAAyC;AAAA,UAC5D,YAAY,KAAK,iBAAiB,IAAI,GAAG;AAAA,UACzC,aAAa;AAAA,QACjB,CAAC;AAAA,MACL;AACA,UAAI,OAAO,UAAU,YAAY,MAAM,WAAW;AAC9C,YAAI,KAAK,wBAAwB,IAAI,QAAQ,KACzC,KAAK,wBAAwB,IAAI,QAAQ,MAAM,MAAM,WAAW;AAChE,gBAAM,IAAI,aAAa,6CAA6C;AAAA,YAChE;AAAA,UACJ,CAAC;AAAA,QACL;AACA,aAAK,wBAAwB,IAAI,UAAU,MAAM,SAAS;AAAA,MAC9D;AACA,WAAK,iBAAiB,IAAI,KAAK,QAAQ;AACvC,WAAK,kBAAkB,IAAI,KAAK,SAAS;AACzC,UAAI,gBAAgB,SAAS,GAAG;AAC5B,cAAM,iBAAiB,qDACV,gBAAgB,KAAK,IAAI,CAAC;AAAA;AAEvC,YAAI,OAAuC;AAGvC,kBAAQ,KAAK,cAAc;AAAA,QAC/B,OACK;AACD,iBAAO,KAAK,cAAc;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QAAQ,OAAO;AAGX,WAAO,UAAU,OAAO,YAAY;AAChC,YAAM,sBAAsB,IAAI,4BAA4B;AAC5D,WAAK,SAAS,QAAQ,KAAK,mBAAmB;AAG9C,iBAAW,CAAC,KAAK,QAAQ,KAAK,KAAK,kBAAkB;AACjD,cAAM,YAAY,KAAK,wBAAwB,IAAI,QAAQ;AAC3D,cAAM,YAAY,KAAK,kBAAkB,IAAI,GAAG;AAChD,cAAM,UAAU,IAAI,QAAQ,KAAK;AAAA,UAC7B;AAAA,UACA,OAAO;AAAA,UACP,aAAa;AAAA,QACjB,CAAC;AACD,cAAM,QAAQ,IAAI,KAAK,SAAS,UAAU;AAAA,UACtC,QAAQ,EAAE,SAAS;AAAA,UACnB;AAAA,UACA;AAAA,QACJ,CAAC,CAAC;AAAA,MACN;AACA,YAAM,EAAE,aAAa,eAAe,IAAI;AACxC,UAAI,MAAuC;AACvC,4BAAoB,aAAa,cAAc;AAAA,MACnD;AACA,aAAO,EAAE,aAAa,eAAe;AAAA,IACzC,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAAS,OAAO;AAGZ,WAAO,UAAU,OAAO,YAAY;AAChC,YAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,KAAK,SAAS,SAAS;AAC5D,YAAM,0BAA0B,MAAM,MAAM,KAAK;AACjD,YAAM,oBAAoB,IAAI,IAAI,KAAK,iBAAiB,OAAO,CAAC;AAChE,YAAM,cAAc,CAAC;AACrB,iBAAW,WAAW,yBAAyB;AAC3C,YAAI,CAAC,kBAAkB,IAAI,QAAQ,GAAG,GAAG;AACrC,gBAAM,MAAM,OAAO,OAAO;AAC1B,sBAAY,KAAK,QAAQ,GAAG;AAAA,QAChC;AAAA,MACJ;AACA,UAAI,MAAuC;AACvC,4BAAoB,WAAW;AAAA,MACnC;AACA,aAAO,EAAE,YAAY;AAAA,IACzB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACjB,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACZ,WAAO,CAAC,GAAG,KAAK,iBAAiB,KAAK,CAAC;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,KAAK;AACnB,UAAM,YAAY,IAAI,IAAI,KAAK,SAAS,IAAI;AAC5C,WAAO,KAAK,iBAAiB,IAAI,UAAU,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,wBAAwB,UAAU;AAC9B,WAAO,KAAK,wBAAwB,IAAI,QAAQ;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,MAAM,cAAc,SAAS;AACzB,UAAM,MAAM,mBAAmB,UAAU,QAAQ,MAAM;AACvD,UAAM,WAAW,KAAK,kBAAkB,GAAG;AAC3C,QAAI,UAAU;AACV,YAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,KAAK,SAAS,SAAS;AAC5D,aAAO,MAAM,MAAM,QAAQ;AAAA,IAC/B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,wBAAwB,KAAK;AACzB,UAAM,WAAW,KAAK,kBAAkB,GAAG;AAC3C,QAAI,CAAC,UAAU;AACX,YAAM,IAAI,aAAa,qBAAqB,EAAE,IAAI,CAAC;AAAA,IACvD;AACA,WAAO,CAAC,YAAY;AAChB,cAAQ,UAAU,IAAI,QAAQ,GAAG;AACjC,cAAQ,SAAS,OAAO,OAAO,EAAE,SAAS,GAAG,QAAQ,MAAM;AAC3D,aAAO,KAAK,SAAS,OAAO,OAAO;AAAA,IACvC;AAAA,EACJ;AACJ;;;ACzRA,IAAI;AAKG,IAAM,gCAAgC,MAAM;AAC/C,MAAI,CAAC,oBAAoB;AACrB,yBAAqB,IAAI,mBAAmB;AAAA,EAChD;AACA,SAAO;AACX;;;ACHA,SAAS,WAAW,SAAS;AACzB,QAAMC,sBAAqB,8BAA8B;AACzD,EAAAA,oBAAmB,SAAS,QAAQ,KAAK,GAAG,OAAO;AACvD;;;ACCO,SAAS,0BAA0B,WAAW,8BAA8B,CAAC,GAAG;AAGnF,aAAW,aAAa,CAAC,GAAG,UAAU,aAAa,KAAK,CAAC,GAAG;AACxD,QAAI,4BAA4B,KAAK,CAAC,WAAW,OAAO,KAAK,SAAS,CAAC,GAAG;AACtE,gBAAU,aAAa,OAAO,SAAS;AAAA,IAC3C;AAAA,EACJ;AACA,SAAO;AACX;;;ACVO,UAAU,sBAAsB,KAAK,EAAE,8BAA8B,CAAC,SAAS,UAAU,GAAG,iBAAiB,cAAc,YAAY,MAAM,gBAAiB,IAAI,CAAC,GAAG;AACzK,QAAM,YAAY,IAAI,IAAI,KAAK,SAAS,IAAI;AAC5C,YAAU,OAAO;AACjB,QAAM,UAAU;AAChB,QAAM,0BAA0B,0BAA0B,WAAW,2BAA2B;AAChG,QAAM,wBAAwB;AAC9B,MAAI,kBAAkB,wBAAwB,SAAS,SAAS,GAAG,GAAG;AAClE,UAAM,eAAe,IAAI,IAAI,wBAAwB,IAAI;AACzD,iBAAa,YAAY;AACzB,UAAM,aAAa;AAAA,EACvB;AACA,MAAI,WAAW;AACX,UAAM,WAAW,IAAI,IAAI,wBAAwB,IAAI;AACrD,aAAS,YAAY;AACrB,UAAM,SAAS;AAAA,EACnB;AACA,MAAI,iBAAiB;AACjB,UAAM,iBAAiB,gBAAgB,EAAE,KAAK,UAAU,CAAC;AACzD,eAAW,gBAAgB,gBAAgB;AACvC,YAAM,aAAa;AAAA,IACvB;AAAA,EACJ;AACJ;;;ACpBA,IAAM,gBAAN,cAA4B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiB9B,YAAYC,qBAAoB,SAAS;AACrC,UAAM,QAAQ,CAAC,EAAE,QAAS,MAAM;AAC5B,YAAM,kBAAkBA,oBAAmB,mBAAmB;AAC9D,iBAAW,eAAe,sBAAsB,QAAQ,KAAK,OAAO,GAAG;AACnE,cAAM,WAAW,gBAAgB,IAAI,WAAW;AAChD,YAAI,UAAU;AACV,gBAAM,YAAYA,oBAAmB,wBAAwB,QAAQ;AACrE,iBAAO,EAAE,UAAU,UAAU;AAAA,QACjC;AAAA,MACJ;AACA,UAAI,MAAuC;AACvC,eAAO,MAAM,yCAAyC,eAAe,QAAQ,GAAG,CAAC;AAAA,MACrF;AACA;AAAA,IACJ;AACA,UAAM,OAAOA,oBAAmB,QAAQ;AAAA,EAC5C;AACJ;;;AC9BA,SAAS,SAAS,SAAS;AACvB,QAAMC,sBAAqB,8BAA8B;AACzD,QAAM,gBAAgB,IAAI,cAAcA,qBAAoB,OAAO;AACnE,gBAAc,aAAa;AAC/B;;;ACrBA,IAAM,oBAAoB;AAmB1B,IAAM,uBAAuB,OAAO,qBAAqB,kBAAkB,sBAAsB;AAC7F,QAAMC,cAAa,MAAM,KAAK,OAAO,KAAK;AAC1C,QAAM,qBAAqBA,YAAW,OAAO,CAAC,cAAc;AACxD,WAAQ,UAAU,SAAS,eAAe,KACtC,UAAU,SAAS,KAAK,aAAa,KAAK,KAC1C,cAAc;AAAA,EACtB,CAAC;AACD,QAAM,QAAQ,IAAI,mBAAmB,IAAI,CAAC,cAAc,KAAK,OAAO,OAAO,SAAS,CAAC,CAAC;AACtF,SAAO;AACX;;;ACnBA,SAAS,wBAAwB;AAE7B,OAAK,iBAAiB,YAAa,CAAC,UAAU;AAC1C,UAAM,YAAY,WAAW,gBAAgB;AAC7C,UAAM,UAAU,qBAAqB,SAAS,EAAE,KAAK,CAAC,kBAAkB;AACpE,UAAI,MAAuC;AACvC,YAAI,cAAc,SAAS,GAAG;AAC1B,iBAAO,IAAI,sEACW,aAAa;AAAA,QACvC;AAAA,MACJ;AAAA,IACJ,CAAC,CAAC;AAAA,EACN,CAAE;AACN;;;ACJA,SAAS,wBAAwB,KAAK;AAClC,QAAMC,sBAAqB,8BAA8B;AACzD,SAAOA,oBAAmB,wBAAwB,GAAG;AACzD;;;ACDA,SAAS,kBAAkB,KAAK;AAC5B,QAAMC,sBAAqB,8BAA8B;AACzD,SAAOA,oBAAmB,kBAAkB,GAAG;AACnD;;;ACPA,SAAS,cAAc,SAAS;AAC5B,QAAMC,sBAAqB,8BAA8B;AACzD,SAAOA,oBAAmB,cAAc,OAAO;AACnD;;;ACCA,SAAS,SAAS,SAAS;AACvB,QAAMC,sBAAqB,8BAA8B;AACzD,EAAAA,oBAAmB,SAAS,OAAO;AACvC;;;ACPA,SAAS,iBAAiB,SAAS,SAAS;AACxC,WAAS,OAAO;AAChB,WAAS,OAAO;AACpB;;;ACJA,IAAM,yBAAN,MAA6B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWzB,YAAY,EAAE,aAAa,oBAAAC,oBAAoB,GAAG;AAM9C,SAAK,kBAAkB,MAAM,KAAK,oBAAoB,cAAc,KAAK,YAAY;AACrF,SAAK,eAAe;AACpB,SAAK,sBACDA,uBAAsB,8BAA8B;AAAA,EAC5D;AACJ;", "names": ["precacheController", "precacheController", "precacheController", "precacheController", "cacheNames", "precacheController", "precacheController", "precacheController", "precacheController", "precacheController"]}