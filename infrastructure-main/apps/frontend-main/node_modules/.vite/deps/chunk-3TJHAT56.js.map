{"version": 3, "sources": ["../../workbox-core/registerQuotaErrorCallback.js", "../../workbox-core/_private/dontWaitFor.js"], "sourcesContent": ["/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport { assert } from './_private/assert.js';\nimport { quotaErrorCallbacks } from './models/quotaErrorCallbacks.js';\nimport './_version.js';\n/**\n * Adds a function to the set of quotaErrorCallbacks that will be executed if\n * there's a quota error.\n *\n * @param {Function} callback\n * @memberof workbox-core\n */\n// Can't change Function type\n// eslint-disable-next-line @typescript-eslint/ban-types\nfunction registerQuotaErrorCallback(callback) {\n    if (process.env.NODE_ENV !== 'production') {\n        assert.isType(callback, 'function', {\n            moduleName: 'workbox-core',\n            funcName: 'register',\n            paramName: 'callback',\n        });\n    }\n    quotaErrorCallbacks.add(callback);\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Registered a callback to respond to quota errors.', callback);\n    }\n}\nexport { registerQuotaErrorCallback };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A helper function that prevents a promise from being flagged as unused.\n *\n * @private\n **/\nexport function dontWaitFor(promise) {\n    // Effective no-op.\n    void promise.then(() => { });\n}\n"], "mappings": ";;;;;;;;;AAoBA,SAAS,2BAA2B,UAAU;AAC1C,MAAI,MAAuC;AACvC,uBAAO,OAAO,UAAU,YAAY;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,IACf,CAAC;AAAA,EACL;AACA,sBAAoB,IAAI,QAAQ;AAChC,MAAI,MAAuC;AACvC,WAAO,IAAI,qDAAqD,QAAQ;AAAA,EAC5E;AACJ;;;ACpBO,SAAS,YAAY,SAAS;AAEjC,OAAK,QAAQ,KAAK,MAAM;AAAA,EAAE,CAAC;AAC/B;", "names": []}