{"version": 3, "sources": ["../../workbox-core/_private.js", "../../workbox-core/_private/canConstructReadableStream.js", "../../workbox-core/_private/resultingClientExists.js", "../../workbox-core/cacheNames.js", "../../workbox-core/clientsClaim.js", "../../workbox-core/setCacheNameDetails.js", "../../workbox-core/skipWaiting.js"], "sourcesContent": ["/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\n// We either expose defaults or we expose every named export.\nimport { assert } from './_private/assert.js';\nimport { cacheNames } from './_private/cacheNames.js';\nimport { cacheMatchIgnoreParams } from './_private/cacheMatchIgnoreParams.js';\nimport { canConstructReadableStream } from './_private/canConstructReadableStream.js';\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { dontWaitFor } from './_private/dontWaitFor.js';\nimport { Deferred } from './_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from './_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from './_private/getFriendlyURL.js';\nimport { logger } from './_private/logger.js';\nimport { resultingClientExists } from './_private/resultingClientExists.js';\nimport { timeout } from './_private/timeout.js';\nimport { waitUntil } from './_private/waitUntil.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\nexport { assert, cacheMatchIgnoreParams, cacheNames, canConstructReadableStream, canConstructResponseFromBodyStream, dontWaitFor, Deferred, executeQuotaErrorCallbacks, getFriendlyURL, logger, resultingClientExists, timeout, waitUntil, WorkboxError, };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a [`ReadableStream`](https://developer.mozilla.org/en-US/docs/Web/API/ReadableStream/ReadableStream)\n * object.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `ReadableStream`, `false` otherwise.\n *\n * @private\n */\nfunction canConstructReadableStream() {\n    if (supportStatus === undefined) {\n        // See https://github.com/GoogleChrome/workbox/issues/1473\n        try {\n            new ReadableStream({ start() { } });\n            supportStatus = true;\n        }\n        catch (error) {\n            supportStatus = false;\n        }\n    }\n    return supportStatus;\n}\nexport { canConstructReadableStream };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { timeout } from './timeout.js';\nimport '../_version.js';\nconst MAX_RETRY_TIME = 2000;\n/**\n * Returns a promise that resolves to a window client matching the passed\n * `resultingClientId`. For browsers that don't support `resultingClientId`\n * or if waiting for the resulting client to apper takes too long, resolve to\n * `undefined`.\n *\n * @param {string} [resultingClientId]\n * @return {Promise<Client|undefined>}\n * @private\n */\nexport async function resultingClientExists(resultingClientId) {\n    if (!resultingClientId) {\n        return;\n    }\n    let existingWindows = await self.clients.matchAll({ type: 'window' });\n    const existingWindowIds = new Set(existingWindows.map((w) => w.id));\n    let resultingWindow;\n    const startTime = performance.now();\n    // Only wait up to `MAX_RETRY_TIME` to find a matching client.\n    while (performance.now() - startTime < MAX_RETRY_TIME) {\n        existingWindows = await self.clients.matchAll({ type: 'window' });\n        resultingWindow = existingWindows.find((w) => {\n            if (resultingClientId) {\n                // If we have a `resultingClientId`, we can match on that.\n                return w.id === resultingClientId;\n            }\n            else {\n                // Otherwise match on finding a window not in `existingWindowIds`.\n                return !existingWindowIds.has(w.id);\n            }\n        });\n        if (resultingWindow) {\n            break;\n        }\n        // Sleep for 100ms and retry.\n        await timeout(100);\n    }\n    return resultingWindow;\n}\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames as _cacheNames } from './_private/cacheNames.js';\nimport './_version.js';\n/**\n * Get the current cache names and prefix/suffix used by Workbox.\n *\n * `cacheNames.precache` is used for precached assets,\n * `cacheNames.googleAnalytics` is used by `workbox-google-analytics` to\n * store `analytics.js`, and `cacheNames.runtime` is used for everything else.\n *\n * `cacheNames.prefix` can be used to retrieve just the current prefix value.\n * `cacheNames.suffix` can be used to retrieve just the current suffix value.\n *\n * @return {Object} An object with `precache`, `runtime`, `prefix`, and\n *     `googleAnalytics` properties.\n *\n * @memberof workbox-core\n */\nconst cacheNames = {\n    get googleAnalytics() {\n        return _cacheNames.getGoogleAnalyticsName();\n    },\n    get precache() {\n        return _cacheNames.getPrecacheName();\n    },\n    get prefix() {\n        return _cacheNames.getPrefix();\n    },\n    get runtime() {\n        return _cacheNames.getRuntimeName();\n    },\n    get suffix() {\n        return _cacheNames.getSuffix();\n    },\n};\nexport { cacheNames };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport './_version.js';\n/**\n * Claim any currently available clients once the service worker\n * becomes active. This is normally used in conjunction with `skipWaiting()`.\n *\n * @memberof workbox-core\n */\nfunction clientsClaim() {\n    self.addEventListener('activate', () => self.clients.claim());\n}\nexport { clientsClaim };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from './_private/assert.js';\nimport { cacheNames } from './_private/cacheNames.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Modifies the default cache names used by the Workbox packages.\n * Cache names are generated as `<prefix>-<Cache Name>-<suffix>`.\n *\n * @param {Object} details\n * @param {Object} [details.prefix] The string to add to the beginning of\n *     the precache and runtime cache names.\n * @param {Object} [details.suffix] The string to add to the end of\n *     the precache and runtime cache names.\n * @param {Object} [details.precache] The cache name to use for precache\n *     caching.\n * @param {Object} [details.runtime] The cache name to use for runtime caching.\n * @param {Object} [details.googleAnalytics] The cache name to use for\n *     `workbox-google-analytics` caching.\n *\n * @memberof workbox-core\n */\nfunction setCacheNameDetails(details) {\n    if (process.env.NODE_ENV !== 'production') {\n        Object.keys(details).forEach((key) => {\n            assert.isType(details[key], 'string', {\n                moduleName: 'workbox-core',\n                funcName: 'setCacheNameDetails',\n                paramName: `details.${key}`,\n            });\n        });\n        if ('precache' in details && details['precache'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'precache',\n                value: details['precache'],\n            });\n        }\n        if ('runtime' in details && details['runtime'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'runtime',\n                value: details['runtime'],\n            });\n        }\n        if ('googleAnalytics' in details &&\n            details['googleAnalytics'].length === 0) {\n            throw new WorkboxError('invalid-cache-name', {\n                cacheNameId: 'googleAnalytics',\n                value: details['googleAnalytics'],\n            });\n        }\n    }\n    cacheNames.updateDetails(details);\n}\nexport { setCacheNameDetails };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from './_private/logger.js';\nimport './_version.js';\n/**\n * This method is deprecated, and will be removed in Workbox v7.\n *\n * Calling self.skipWaiting() is equivalent, and should be used instead.\n *\n * @memberof workbox-core\n */\nfunction skipWaiting() {\n    // Just call self.skipWaiting() directly.\n    // See https://github.com/GoogleChrome/workbox/issues/2525\n    if (process.env.NODE_ENV !== 'production') {\n        logger.warn(`skipWaiting() from workbox-core is no longer recommended ` +\n            `and will be removed in Workbox v7. Using self.skipWaiting() instead ` +\n            `is equivalent.`);\n    }\n    void self.skipWaiting();\n}\nexport { skipWaiting };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACQA,IAAI;AAWJ,SAAS,6BAA6B;AAClC,MAAI,kBAAkB,QAAW;AAE7B,QAAI;AACA,UAAI,eAAe,EAAE,QAAQ;AAAA,MAAE,EAAE,CAAC;AAClC,sBAAgB;AAAA,IACpB,SACO,OAAO;AACV,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;;;ACvBA,IAAM,iBAAiB;AAWvB,eAAsB,sBAAsB,mBAAmB;AAC3D,MAAI,CAAC,mBAAmB;AACpB;AAAA,EACJ;AACA,MAAI,kBAAkB,MAAM,KAAK,QAAQ,SAAS,EAAE,MAAM,SAAS,CAAC;AACpE,QAAM,oBAAoB,IAAI,IAAI,gBAAgB,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;AAClE,MAAI;AACJ,QAAM,YAAY,YAAY,IAAI;AAElC,SAAO,YAAY,IAAI,IAAI,YAAY,gBAAgB;AACnD,sBAAkB,MAAM,KAAK,QAAQ,SAAS,EAAE,MAAM,SAAS,CAAC;AAChE,sBAAkB,gBAAgB,KAAK,CAAC,MAAM;AAC1C,UAAI,mBAAmB;AAEnB,eAAO,EAAE,OAAO;AAAA,MACpB,OACK;AAED,eAAO,CAAC,kBAAkB,IAAI,EAAE,EAAE;AAAA,MACtC;AAAA,IACJ,CAAC;AACD,QAAI,iBAAiB;AACjB;AAAA,IACJ;AAEA,UAAM,QAAQ,GAAG;AAAA,EACrB;AACA,SAAO;AACX;;;ACvBA,IAAMA,cAAa;AAAA,EACf,IAAI,kBAAkB;AAClB,WAAO,WAAY,uBAAuB;AAAA,EAC9C;AAAA,EACA,IAAI,WAAW;AACX,WAAO,WAAY,gBAAgB;AAAA,EACvC;AAAA,EACA,IAAI,SAAS;AACT,WAAO,WAAY,UAAU;AAAA,EACjC;AAAA,EACA,IAAI,UAAU;AACV,WAAO,WAAY,eAAe;AAAA,EACtC;AAAA,EACA,IAAI,SAAS;AACT,WAAO,WAAY,UAAU;AAAA,EACjC;AACJ;;;AC1BA,SAAS,eAAe;AACpB,OAAK,iBAAiB,YAAY,MAAM,KAAK,QAAQ,MAAM,CAAC;AAChE;;;ACYA,SAAS,oBAAoB,SAAS;AAClC,MAAI,MAAuC;AACvC,WAAO,KAAK,OAAO,EAAE,QAAQ,CAAC,QAAQ;AAClC,yBAAO,OAAO,QAAQ,GAAG,GAAG,UAAU;AAAA,QAClC,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,WAAW,WAAW,GAAG;AAAA,MAC7B,CAAC;AAAA,IACL,CAAC;AACD,QAAI,cAAc,WAAW,QAAQ,UAAU,EAAE,WAAW,GAAG;AAC3D,YAAM,IAAI,aAAa,sBAAsB;AAAA,QACzC,aAAa;AAAA,QACb,OAAO,QAAQ,UAAU;AAAA,MAC7B,CAAC;AAAA,IACL;AACA,QAAI,aAAa,WAAW,QAAQ,SAAS,EAAE,WAAW,GAAG;AACzD,YAAM,IAAI,aAAa,sBAAsB;AAAA,QACzC,aAAa;AAAA,QACb,OAAO,QAAQ,SAAS;AAAA,MAC5B,CAAC;AAAA,IACL;AACA,QAAI,qBAAqB,WACrB,QAAQ,iBAAiB,EAAE,WAAW,GAAG;AACzC,YAAM,IAAI,aAAa,sBAAsB;AAAA,QACzC,aAAa;AAAA,QACb,OAAO,QAAQ,iBAAiB;AAAA,MACpC,CAAC;AAAA,IACL;AAAA,EACJ;AACA,aAAW,cAAc,OAAO;AACpC;;;AC1CA,SAAS,cAAc;AAGnB,MAAI,MAAuC;AACvC,WAAO,KAAK,6IAEQ;AAAA,EACxB;AACA,OAAK,KAAK,YAAY;AAC1B;", "names": ["cacheNames"]}