import {
  canConstructResponseFromBodyStream,
  copyResponse,
  waitUntil
} from "./chunk-JPUOFCPC.js";
import {
  Deferred,
  cacheMatchIgnoreParams,
  executeQuotaErrorCallbacks,
  timeout
} from "./chunk-Y6342WCD.js";
import {
  dontWaitFor,
  registerQuotaErrorCallback
} from "./chunk-3TJHAT56.js";
import {
  cacheNames
} from "./chunk-JC2IFURB.js";
import {
  WorkboxError,
  finalAssertExports,
  getFriendlyURL,
  logger
} from "./chunk-FWAXG7AD.js";
import {
  __export
} from "./chunk-HM4MQYWN.js";

// node_modules/workbox-core/_private.js
var private_exports = {};
__export(private_exports, {
  Deferred: () => Deferred,
  WorkboxError: () => WorkboxError,
  assert: () => finalAssertExports,
  cacheMatchIgnoreParams: () => cacheMatchIgnoreParams,
  cacheNames: () => cacheNames,
  canConstructReadableStream: () => canConstructReadableStream,
  canConstructResponseFromBodyStream: () => canConstructResponseFromBodyStream,
  dontWaitFor: () => dontWaitFor,
  executeQuotaErrorCallbacks: () => executeQuotaErrorCallbacks,
  getFriendlyURL: () => getFriendlyURL,
  logger: () => logger,
  resultingClientExists: () => resultingClientExists,
  timeout: () => timeout,
  waitUntil: () => waitUntil
});

// node_modules/workbox-core/_private/canConstructReadableStream.js
var supportStatus;
function canConstructReadableStream() {
  if (supportStatus === void 0) {
    try {
      new ReadableStream({ start() {
      } });
      supportStatus = true;
    } catch (error) {
      supportStatus = false;
    }
  }
  return supportStatus;
}

// node_modules/workbox-core/_private/resultingClientExists.js
var MAX_RETRY_TIME = 2e3;
async function resultingClientExists(resultingClientId) {
  if (!resultingClientId) {
    return;
  }
  let existingWindows = await self.clients.matchAll({ type: "window" });
  const existingWindowIds = new Set(existingWindows.map((w) => w.id));
  let resultingWindow;
  const startTime = performance.now();
  while (performance.now() - startTime < MAX_RETRY_TIME) {
    existingWindows = await self.clients.matchAll({ type: "window" });
    resultingWindow = existingWindows.find((w) => {
      if (resultingClientId) {
        return w.id === resultingClientId;
      } else {
        return !existingWindowIds.has(w.id);
      }
    });
    if (resultingWindow) {
      break;
    }
    await timeout(100);
  }
  return resultingWindow;
}

// node_modules/workbox-core/cacheNames.js
var cacheNames2 = {
  get googleAnalytics() {
    return cacheNames.getGoogleAnalyticsName();
  },
  get precache() {
    return cacheNames.getPrecacheName();
  },
  get prefix() {
    return cacheNames.getPrefix();
  },
  get runtime() {
    return cacheNames.getRuntimeName();
  },
  get suffix() {
    return cacheNames.getSuffix();
  }
};

// node_modules/workbox-core/clientsClaim.js
function clientsClaim() {
  self.addEventListener("activate", () => self.clients.claim());
}

// node_modules/workbox-core/setCacheNameDetails.js
function setCacheNameDetails(details) {
  if (true) {
    Object.keys(details).forEach((key) => {
      finalAssertExports.isType(details[key], "string", {
        moduleName: "workbox-core",
        funcName: "setCacheNameDetails",
        paramName: `details.${key}`
      });
    });
    if ("precache" in details && details["precache"].length === 0) {
      throw new WorkboxError("invalid-cache-name", {
        cacheNameId: "precache",
        value: details["precache"]
      });
    }
    if ("runtime" in details && details["runtime"].length === 0) {
      throw new WorkboxError("invalid-cache-name", {
        cacheNameId: "runtime",
        value: details["runtime"]
      });
    }
    if ("googleAnalytics" in details && details["googleAnalytics"].length === 0) {
      throw new WorkboxError("invalid-cache-name", {
        cacheNameId: "googleAnalytics",
        value: details["googleAnalytics"]
      });
    }
  }
  cacheNames.updateDetails(details);
}

// node_modules/workbox-core/skipWaiting.js
function skipWaiting() {
  if (true) {
    logger.warn(`skipWaiting() from workbox-core is no longer recommended and will be removed in Workbox v7. Using self.skipWaiting() instead is equivalent.`);
  }
  void self.skipWaiting();
}
export {
  private_exports as _private,
  cacheNames2 as cacheNames,
  clientsClaim,
  copyResponse,
  registerQuotaErrorCallback,
  setCacheNameDetails,
  skipWaiting
};
//# sourceMappingURL=workbox-core.js.map
