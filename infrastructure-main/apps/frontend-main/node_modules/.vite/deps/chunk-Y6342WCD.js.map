{"version": 3, "sources": ["../../workbox-core/_private/cacheMatchIgnoreParams.js", "../../workbox-core/_private/Deferred.js", "../../workbox-core/_private/executeQuotaErrorCallbacks.js", "../../workbox-core/_private/timeout.js"], "sourcesContent": ["/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nfunction stripParams(fullURL, ignoreParams) {\n    const strippedURL = new URL(fullURL);\n    for (const param of ignoreParams) {\n        strippedURL.searchParams.delete(param);\n    }\n    return strippedURL.href;\n}\n/**\n * Matches an item in the cache, ignoring specific URL params. This is similar\n * to the `ignoreSearch` option, but it allows you to ignore just specific\n * params (while continuing to match on the others).\n *\n * @private\n * @param {Cache} cache\n * @param {Request} request\n * @param {Object} matchOptions\n * @param {Array<string>} ignoreParams\n * @return {Promise<Response|undefined>}\n */\nasync function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {\n    const strippedRequestURL = stripParams(request.url, ignoreParams);\n    // If the request doesn't include any ignored params, match as normal.\n    if (request.url === strippedRequestURL) {\n        return cache.match(request, matchOptions);\n    }\n    // Otherwise, match by comparing keys\n    const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });\n    const cacheKeys = await cache.keys(request, keysOptions);\n    for (const cacheKey of cacheKeys) {\n        const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);\n        if (strippedRequestURL === strippedCacheKeyURL) {\n            return cache.match(cacheKey, matchOptions);\n        }\n    }\n    return;\n}\nexport { cacheMatchIgnoreParams };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The Deferred class composes Promises in a way that allows for them to be\n * resolved or rejected from outside the constructor. In most cases promises\n * should be used directly, but Deferreds can be necessary when the logic to\n * resolve a promise must be separate.\n *\n * @private\n */\nclass Deferred {\n    /**\n     * Creates a promise and exposes its resolve and reject functions as methods.\n     */\n    constructor() {\n        this.promise = new Promise((resolve, reject) => {\n            this.resolve = resolve;\n            this.reject = reject;\n        });\n    }\n}\nexport { Deferred };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from '../_private/logger.js';\nimport { quotaErrorCallbacks } from '../models/quotaErrorCallbacks.js';\nimport '../_version.js';\n/**\n * Runs all of the callback functions, one at a time sequentially, in the order\n * in which they were registered.\n *\n * @memberof workbox-core\n * @private\n */\nasync function executeQuotaErrorCallbacks() {\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log(`About to run ${quotaErrorCallbacks.size} ` +\n            `callbacks to clean up caches.`);\n    }\n    for (const callback of quotaErrorCallbacks) {\n        await callback();\n        if (process.env.NODE_ENV !== 'production') {\n            logger.log(callback, 'is complete.');\n        }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        logger.log('Finished running callbacks.');\n    }\n}\nexport { executeQuotaErrorCallbacks };\n", "/*\n  Copyright 2019 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * Returns a promise that resolves and the passed number of milliseconds.\n * This utility is an async/await-friendly version of `setTimeout`.\n *\n * @param {number} ms\n * @return {Promise}\n * @private\n */\nexport function timeout(ms) {\n    return new Promise((resolve) => setTimeout(resolve, ms));\n}\n"], "mappings": ";;;;;;;;AAOA,SAAS,YAAY,SAAS,cAAc;AACxC,QAAM,cAAc,IAAI,IAAI,OAAO;AACnC,aAAW,SAAS,cAAc;AAC9B,gBAAY,aAAa,OAAO,KAAK;AAAA,EACzC;AACA,SAAO,YAAY;AACvB;AAaA,eAAe,uBAAuB,OAAO,SAAS,cAAc,cAAc;AAC9E,QAAM,qBAAqB,YAAY,QAAQ,KAAK,YAAY;AAEhE,MAAI,QAAQ,QAAQ,oBAAoB;AACpC,WAAO,MAAM,MAAM,SAAS,YAAY;AAAA,EAC5C;AAEA,QAAM,cAAc,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,cAAc,KAAK,CAAC;AACzF,QAAM,YAAY,MAAM,MAAM,KAAK,SAAS,WAAW;AACvD,aAAW,YAAY,WAAW;AAC9B,UAAM,sBAAsB,YAAY,SAAS,KAAK,YAAY;AAClE,QAAI,uBAAuB,qBAAqB;AAC5C,aAAO,MAAM,MAAM,UAAU,YAAY;AAAA,IAC7C;AAAA,EACJ;AACA;AACJ;;;AC1BA,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,EAIX,cAAc;AACV,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC5C,WAAK,UAAU;AACf,WAAK,SAAS;AAAA,IAClB,CAAC;AAAA,EACL;AACJ;;;ACTA,eAAe,6BAA6B;AACxC,MAAI,MAAuC;AACvC,WAAO,IAAI,gBAAgB,oBAAoB,IAAI,gCAChB;AAAA,EACvC;AACA,aAAW,YAAY,qBAAqB;AACxC,UAAM,SAAS;AACf,QAAI,MAAuC;AACvC,aAAO,IAAI,UAAU,cAAc;AAAA,IACvC;AAAA,EACJ;AACA,MAAI,MAAuC;AACvC,WAAO,IAAI,6BAA6B;AAAA,EAC5C;AACJ;;;AChBO,SAAS,QAAQ,IAAI;AACxB,SAAO,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;AAC3D;", "names": []}