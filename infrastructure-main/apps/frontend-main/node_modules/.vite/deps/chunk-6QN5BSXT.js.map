{"version": 3, "sources": ["../../workbox-strategies/_version.js", "../../workbox-strategies/StrategyHandler.js", "../../workbox-strategies/Strategy.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:strategies:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { cacheMatchIgnoreParams } from 'workbox-core/_private/cacheMatchIgnoreParams.js';\nimport { Deferred } from 'workbox-core/_private/Deferred.js';\nimport { executeQuotaErrorCallbacks } from 'workbox-core/_private/executeQuotaErrorCallbacks.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { timeout } from 'workbox-core/_private/timeout.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\nfunction toRequest(input) {\n    return typeof input === 'string' ? new Request(input) : input;\n}\n/**\n * A class created every time a Strategy instance instance calls\n * {@link workbox-strategies.Strategy~handle} or\n * {@link workbox-strategies.Strategy~handleAll} that wraps all fetch and\n * cache actions around plugin callbacks and keeps track of when the strategy\n * is \"done\" (i.e. all added `event.waitUntil()` promises have resolved).\n *\n * @memberof workbox-strategies\n */\nclass StrategyHandler {\n    /**\n     * Creates a new instance associated with the passed strategy and event\n     * that's handling the request.\n     *\n     * The constructor also initializes the state that will be passed to each of\n     * the plugins handling this request.\n     *\n     * @param {workbox-strategies.Strategy} strategy\n     * @param {Object} options\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params] The return value from the\n     *     {@link workbox-routing~matchCallback} (if applicable).\n     */\n    constructor(strategy, options) {\n        this._cacheKeys = {};\n        /**\n         * The request the strategy is performing (passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * @name request\n         * @instance\n         * @type {Request}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * The event associated with this request.\n         * @name event\n         * @instance\n         * @type {ExtendableEvent}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `URL` instance of `request.url` (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `url` param will be present if the strategy was invoked\n         * from a workbox `Route` object.\n         * @name url\n         * @instance\n         * @type {URL|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        /**\n         * A `param` value (if passed to the strategy's\n         * `handle()` or `handleAll()` method).\n         * Note: the `param` param will be present if the strategy was invoked\n         * from a workbox `Route` object and the\n         * {@link workbox-routing~matchCallback} returned\n         * a truthy value (it will be that value).\n         * @name params\n         * @instance\n         * @type {*|undefined}\n         * @memberof workbox-strategies.StrategyHandler\n         */\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(options.event, ExtendableEvent, {\n                moduleName: 'workbox-strategies',\n                className: 'StrategyHandler',\n                funcName: 'constructor',\n                paramName: 'options.event',\n            });\n        }\n        Object.assign(this, options);\n        this.event = options.event;\n        this._strategy = strategy;\n        this._handlerDeferred = new Deferred();\n        this._extendLifetimePromises = [];\n        // Copy the plugins list (since it's mutable on the strategy),\n        // so any mutations don't affect this handler instance.\n        this._plugins = [...strategy.plugins];\n        this._pluginStateMap = new Map();\n        for (const plugin of this._plugins) {\n            this._pluginStateMap.set(plugin, {});\n        }\n        this.event.waitUntil(this._handlerDeferred.promise);\n    }\n    /**\n     * Fetches a given request (and invokes any applicable plugin callback\n     * methods) using the `fetchOptions` (for non-navigation requests) and\n     * `plugins` defined on the `Strategy` object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - `requestWillFetch()`\n     * - `fetchDidSucceed()`\n     * - `fetchDidFail()`\n     *\n     * @param {Request|string} input The URL or request to fetch.\n     * @return {Promise<Response>}\n     */\n    async fetch(input) {\n        const { event } = this;\n        let request = toRequest(input);\n        if (request.mode === 'navigate' &&\n            event instanceof FetchEvent &&\n            event.preloadResponse) {\n            const possiblePreloadResponse = (await event.preloadResponse);\n            if (possiblePreloadResponse) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.log(`Using a preloaded navigation response for ` +\n                        `'${getFriendlyURL(request.url)}'`);\n                }\n                return possiblePreloadResponse;\n            }\n        }\n        // If there is a fetchDidFail plugin, we need to save a clone of the\n        // original request before it's either modified by a requestWillFetch\n        // plugin or before the original request's body is consumed via fetch().\n        const originalRequest = this.hasCallback('fetchDidFail')\n            ? request.clone()\n            : null;\n        try {\n            for (const cb of this.iterateCallbacks('requestWillFetch')) {\n                request = await cb({ request: request.clone(), event });\n            }\n        }\n        catch (err) {\n            if (err instanceof Error) {\n                throw new WorkboxError('plugin-error-request-will-fetch', {\n                    thrownErrorMessage: err.message,\n                });\n            }\n        }\n        // The request can be altered by plugins with `requestWillFetch` making\n        // the original request (most likely from a `fetch` event) different\n        // from the Request we make. Pass both to `fetchDidFail` to aid debugging.\n        const pluginFilteredRequest = request.clone();\n        try {\n            let fetchResponse;\n            // See https://github.com/GoogleChrome/workbox/issues/1796\n            fetchResponse = await fetch(request, request.mode === 'navigate' ? undefined : this._strategy.fetchOptions);\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' returned a response with ` +\n                    `status '${fetchResponse.status}'.`);\n            }\n            for (const callback of this.iterateCallbacks('fetchDidSucceed')) {\n                fetchResponse = await callback({\n                    event,\n                    request: pluginFilteredRequest,\n                    response: fetchResponse,\n                });\n            }\n            return fetchResponse;\n        }\n        catch (error) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.log(`Network request for ` +\n                    `'${getFriendlyURL(request.url)}' threw an error.`, error);\n            }\n            // `originalRequest` will only exist if a `fetchDidFail` callback\n            // is being used (see above).\n            if (originalRequest) {\n                await this.runCallbacks('fetchDidFail', {\n                    error: error,\n                    event,\n                    originalRequest: originalRequest.clone(),\n                    request: pluginFilteredRequest.clone(),\n                });\n            }\n            throw error;\n        }\n    }\n    /**\n     * Calls `this.fetch()` and (in the background) runs `this.cachePut()` on\n     * the response generated by `this.fetch()`.\n     *\n     * The call to `this.cachePut()` automatically invokes `this.waitUntil()`,\n     * so you do not have to manually call `waitUntil()` on the event.\n     *\n     * @param {Request|string} input The request or URL to fetch and cache.\n     * @return {Promise<Response>}\n     */\n    async fetchAndCachePut(input) {\n        const response = await this.fetch(input);\n        const responseClone = response.clone();\n        void this.waitUntil(this.cachePut(input, responseClone));\n        return response;\n    }\n    /**\n     * Matches a request from the cache (and invokes any applicable plugin\n     * callback methods) using the `cacheName`, `matchOptions`, and `plugins`\n     * defined on the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cachedResponseWillBeUsed()\n     *\n     * @param {Request|string} key The Request or URL to use as the cache key.\n     * @return {Promise<Response|undefined>} A matching response, if found.\n     */\n    async cacheMatch(key) {\n        const request = toRequest(key);\n        let cachedResponse;\n        const { cacheName, matchOptions } = this._strategy;\n        const effectiveRequest = await this.getCacheKey(request, 'read');\n        const multiMatchOptions = Object.assign(Object.assign({}, matchOptions), { cacheName });\n        cachedResponse = await caches.match(effectiveRequest, multiMatchOptions);\n        if (process.env.NODE_ENV !== 'production') {\n            if (cachedResponse) {\n                logger.debug(`Found a cached response in '${cacheName}'.`);\n            }\n            else {\n                logger.debug(`No cached response found in '${cacheName}'.`);\n            }\n        }\n        for (const callback of this.iterateCallbacks('cachedResponseWillBeUsed')) {\n            cachedResponse =\n                (await callback({\n                    cacheName,\n                    matchOptions,\n                    cachedResponse,\n                    request: effectiveRequest,\n                    event: this.event,\n                })) || undefined;\n        }\n        return cachedResponse;\n    }\n    /**\n     * Puts a request/response pair in the cache (and invokes any applicable\n     * plugin callback methods) using the `cacheName` and `plugins` defined on\n     * the strategy object.\n     *\n     * The following plugin lifecycle methods are invoked when using this method:\n     * - cacheKeyWillBeUsed()\n     * - cacheWillUpdate()\n     * - cacheDidUpdate()\n     *\n     * @param {Request|string} key The request or URL to use as the cache key.\n     * @param {Response} response The response to cache.\n     * @return {Promise<boolean>} `false` if a cacheWillUpdate caused the response\n     * not be cached, and `true` otherwise.\n     */\n    async cachePut(key, response) {\n        const request = toRequest(key);\n        // Run in the next task to avoid blocking other cache reads.\n        // https://github.com/w3c/ServiceWorker/issues/1397\n        await timeout(0);\n        const effectiveRequest = await this.getCacheKey(request, 'write');\n        if (process.env.NODE_ENV !== 'production') {\n            if (effectiveRequest.method && effectiveRequest.method !== 'GET') {\n                throw new WorkboxError('attempt-to-cache-non-get-request', {\n                    url: getFriendlyURL(effectiveRequest.url),\n                    method: effectiveRequest.method,\n                });\n            }\n            // See https://github.com/GoogleChrome/workbox/issues/2818\n            const vary = response.headers.get('Vary');\n            if (vary) {\n                logger.debug(`The response for ${getFriendlyURL(effectiveRequest.url)} ` +\n                    `has a 'Vary: ${vary}' header. ` +\n                    `Consider setting the {ignoreVary: true} option on your strategy ` +\n                    `to ensure cache matching and deletion works as expected.`);\n            }\n        }\n        if (!response) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.error(`Cannot cache non-existent response for ` +\n                    `'${getFriendlyURL(effectiveRequest.url)}'.`);\n            }\n            throw new WorkboxError('cache-put-with-no-response', {\n                url: getFriendlyURL(effectiveRequest.url),\n            });\n        }\n        const responseToCache = await this._ensureResponseSafeToCache(response);\n        if (!responseToCache) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Response '${getFriendlyURL(effectiveRequest.url)}' ` +\n                    `will not be cached.`, responseToCache);\n            }\n            return false;\n        }\n        const { cacheName, matchOptions } = this._strategy;\n        const cache = await self.caches.open(cacheName);\n        const hasCacheUpdateCallback = this.hasCallback('cacheDidUpdate');\n        const oldResponse = hasCacheUpdateCallback\n            ? await cacheMatchIgnoreParams(\n            // TODO(philipwalton): the `__WB_REVISION__` param is a precaching\n            // feature. Consider into ways to only add this behavior if using\n            // precaching.\n            cache, effectiveRequest.clone(), ['__WB_REVISION__'], matchOptions)\n            : null;\n        if (process.env.NODE_ENV !== 'production') {\n            logger.debug(`Updating the '${cacheName}' cache with a new Response ` +\n                `for ${getFriendlyURL(effectiveRequest.url)}.`);\n        }\n        try {\n            await cache.put(effectiveRequest, hasCacheUpdateCallback ? responseToCache.clone() : responseToCache);\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                // See https://developer.mozilla.org/en-US/docs/Web/API/DOMException#exception-QuotaExceededError\n                if (error.name === 'QuotaExceededError') {\n                    await executeQuotaErrorCallbacks();\n                }\n                throw error;\n            }\n        }\n        for (const callback of this.iterateCallbacks('cacheDidUpdate')) {\n            await callback({\n                cacheName,\n                oldResponse,\n                newResponse: responseToCache.clone(),\n                request: effectiveRequest,\n                event: this.event,\n            });\n        }\n        return true;\n    }\n    /**\n     * Checks the list of plugins for the `cacheKeyWillBeUsed` callback, and\n     * executes any of those callbacks found in sequence. The final `Request`\n     * object returned by the last plugin is treated as the cache key for cache\n     * reads and/or writes. If no `cacheKeyWillBeUsed` plugin callbacks have\n     * been registered, the passed request is returned unmodified\n     *\n     * @param {Request} request\n     * @param {string} mode\n     * @return {Promise<Request>}\n     */\n    async getCacheKey(request, mode) {\n        const key = `${request.url} | ${mode}`;\n        if (!this._cacheKeys[key]) {\n            let effectiveRequest = request;\n            for (const callback of this.iterateCallbacks('cacheKeyWillBeUsed')) {\n                effectiveRequest = toRequest(await callback({\n                    mode,\n                    request: effectiveRequest,\n                    event: this.event,\n                    // params has a type any can't change right now.\n                    params: this.params, // eslint-disable-line\n                }));\n            }\n            this._cacheKeys[key] = effectiveRequest;\n        }\n        return this._cacheKeys[key];\n    }\n    /**\n     * Returns true if the strategy has at least one plugin with the given\n     * callback.\n     *\n     * @param {string} name The name of the callback to check for.\n     * @return {boolean}\n     */\n    hasCallback(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (name in plugin) {\n                return true;\n            }\n        }\n        return false;\n    }\n    /**\n     * Runs all plugin callbacks matching the given name, in order, passing the\n     * given param object (merged ith the current plugin state) as the only\n     * argument.\n     *\n     * Note: since this method runs all plugins, it's not suitable for cases\n     * where the return value of a callback needs to be applied prior to calling\n     * the next callback. See\n     * {@link workbox-strategies.StrategyHandler#iterateCallbacks}\n     * below for how to handle that case.\n     *\n     * @param {string} name The name of the callback to run within each plugin.\n     * @param {Object} param The object to pass as the first (and only) param\n     *     when executing each callback. This object will be merged with the\n     *     current plugin state prior to callback execution.\n     */\n    async runCallbacks(name, param) {\n        for (const callback of this.iterateCallbacks(name)) {\n            // TODO(philipwalton): not sure why `any` is needed. It seems like\n            // this should work with `as WorkboxPluginCallbackParam[C]`.\n            await callback(param);\n        }\n    }\n    /**\n     * Accepts a callback and returns an iterable of matching plugin callbacks,\n     * where each callback is wrapped with the current handler state (i.e. when\n     * you call each callback, whatever object parameter you pass it will\n     * be merged with the plugin's current state).\n     *\n     * @param {string} name The name fo the callback to run\n     * @return {Array<Function>}\n     */\n    *iterateCallbacks(name) {\n        for (const plugin of this._strategy.plugins) {\n            if (typeof plugin[name] === 'function') {\n                const state = this._pluginStateMap.get(plugin);\n                const statefulCallback = (param) => {\n                    const statefulParam = Object.assign(Object.assign({}, param), { state });\n                    // TODO(philipwalton): not sure why `any` is needed. It seems like\n                    // this should work with `as WorkboxPluginCallbackParam[C]`.\n                    return plugin[name](statefulParam);\n                };\n                yield statefulCallback;\n            }\n        }\n    }\n    /**\n     * Adds a promise to the\n     * [extend lifetime promises]{@link https://w3c.github.io/ServiceWorker/#extendableevent-extend-lifetime-promises}\n     * of the event event associated with the request being handled (usually a\n     * `FetchEvent`).\n     *\n     * Note: you can await\n     * {@link workbox-strategies.StrategyHandler~doneWaiting}\n     * to know when all added promises have settled.\n     *\n     * @param {Promise} promise A promise to add to the extend lifetime promises\n     *     of the event that triggered the request.\n     */\n    waitUntil(promise) {\n        this._extendLifetimePromises.push(promise);\n        return promise;\n    }\n    /**\n     * Returns a promise that resolves once all promises passed to\n     * {@link workbox-strategies.StrategyHandler~waitUntil}\n     * have settled.\n     *\n     * Note: any work done after `doneWaiting()` settles should be manually\n     * passed to an event's `waitUntil()` method (not this handler's\n     * `waitUntil()` method), otherwise the service worker thread my be killed\n     * prior to your work completing.\n     */\n    async doneWaiting() {\n        let promise;\n        while ((promise = this._extendLifetimePromises.shift())) {\n            await promise;\n        }\n    }\n    /**\n     * Stops running the strategy and immediately resolves any pending\n     * `waitUntil()` promises.\n     */\n    destroy() {\n        this._handlerDeferred.resolve(null);\n    }\n    /**\n     * This method will call cacheWillUpdate on the available plugins (or use\n     * status === 200) to determine if the Response is safe and valid to cache.\n     *\n     * @param {Request} options.request\n     * @param {Response} options.response\n     * @return {Promise<Response|undefined>}\n     *\n     * @private\n     */\n    async _ensureResponseSafeToCache(response) {\n        let responseToCache = response;\n        let pluginsUsed = false;\n        for (const callback of this.iterateCallbacks('cacheWillUpdate')) {\n            responseToCache =\n                (await callback({\n                    request: this.request,\n                    response: responseToCache,\n                    event: this.event,\n                })) || undefined;\n            pluginsUsed = true;\n            if (!responseToCache) {\n                break;\n            }\n        }\n        if (!pluginsUsed) {\n            if (responseToCache && responseToCache.status !== 200) {\n                responseToCache = undefined;\n            }\n            if (process.env.NODE_ENV !== 'production') {\n                if (responseToCache) {\n                    if (responseToCache.status !== 200) {\n                        if (responseToCache.status === 0) {\n                            logger.warn(`The response for '${this.request.url}' ` +\n                                `is an opaque response. The caching strategy that you're ` +\n                                `using will not cache opaque responses by default.`);\n                        }\n                        else {\n                            logger.debug(`The response for '${this.request.url}' ` +\n                                `returned a status code of '${response.status}' and won't ` +\n                                `be cached as a result.`);\n                        }\n                    }\n                }\n            }\n        }\n        return responseToCache;\n    }\n}\nexport { StrategyHandler };\n", "/*\n  Copyright 2020 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { cacheNames } from 'workbox-core/_private/cacheNames.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { StrategyHandler } from './StrategyHandler.js';\nimport './_version.js';\n/**\n * An abstract base class that all other strategy classes must extend from:\n *\n * @memberof workbox-strategies\n */\nclass Strategy {\n    /**\n     * Creates a new instance of the strategy and sets all documented option\n     * properties as public instance properties.\n     *\n     * Note: if a custom strategy class extends the base Strategy class and does\n     * not need more than these properties, it does not need to define its own\n     * constructor.\n     *\n     * @param {Object} [options]\n     * @param {string} [options.cacheName] Cache name to store and retrieve\n     * requests. Defaults to the cache names provided by\n     * {@link workbox-core.cacheNames}.\n     * @param {Array<Object>} [options.plugins] [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n     * to use in conjunction with this caching strategy.\n     * @param {Object} [options.fetchOptions] Values passed along to the\n     * [`init`](https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters)\n     * of [non-navigation](https://github.com/GoogleChrome/workbox/issues/1796)\n     * `fetch()` requests made by this strategy.\n     * @param {Object} [options.matchOptions] The\n     * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n     * for any `cache.match()` or `cache.put()` calls made by this strategy.\n     */\n    constructor(options = {}) {\n        /**\n         * Cache name to store and retrieve\n         * requests. Defaults to the cache names provided by\n         * {@link workbox-core.cacheNames}.\n         *\n         * @type {string}\n         */\n        this.cacheName = cacheNames.getRuntimeName(options.cacheName);\n        /**\n         * The list\n         * [Plugins]{@link https://developers.google.com/web/tools/workbox/guides/using-plugins}\n         * used by this strategy.\n         *\n         * @type {Array<Object>}\n         */\n        this.plugins = options.plugins || [];\n        /**\n         * Values passed along to the\n         * [`init`]{@link https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/fetch#Parameters}\n         * of all fetch() requests made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.fetchOptions = options.fetchOptions;\n        /**\n         * The\n         * [`CacheQueryOptions`]{@link https://w3c.github.io/ServiceWorker/#dictdef-cachequeryoptions}\n         * for any `cache.match()` or `cache.put()` calls made by this strategy.\n         *\n         * @type {Object}\n         */\n        this.matchOptions = options.matchOptions;\n    }\n    /**\n     * Perform a request strategy and returns a `Promise` that will resolve with\n     * a `Response`, invoking all relevant plugin callbacks.\n     *\n     * When a strategy instance is registered with a Workbox\n     * {@link workbox-routing.Route}, this method is automatically\n     * called when the route matches.\n     *\n     * Alternatively, this method can be used in a standalone `FetchEvent`\n     * listener by passing it to `event.respondWith()`.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     */\n    handle(options) {\n        const [responseDone] = this.handleAll(options);\n        return responseDone;\n    }\n    /**\n     * Similar to {@link workbox-strategies.Strategy~handle}, but\n     * instead of just returning a `Promise` that resolves to a `Response` it\n     * it will return an tuple of `[response, done]` promises, where the former\n     * (`response`) is equivalent to what `handle()` returns, and the latter is a\n     * Promise that will resolve once any promises that were added to\n     * `event.waitUntil()` as part of performing the strategy have completed.\n     *\n     * You can await the `done` promise to ensure any extra work performed by\n     * the strategy (usually caching responses) completes successfully.\n     *\n     * @param {FetchEvent|Object} options A `FetchEvent` or an object with the\n     *     properties listed below.\n     * @param {Request|string} options.request A request to run this strategy for.\n     * @param {ExtendableEvent} options.event The event associated with the\n     *     request.\n     * @param {URL} [options.url]\n     * @param {*} [options.params]\n     * @return {Array<Promise>} A tuple of [response, done]\n     *     promises that can be used to determine when the response resolves as\n     *     well as when the handler has completed all its work.\n     */\n    handleAll(options) {\n        // Allow for flexible options to be passed.\n        if (options instanceof FetchEvent) {\n            options = {\n                event: options,\n                request: options.request,\n            };\n        }\n        const event = options.event;\n        const request = typeof options.request === 'string'\n            ? new Request(options.request)\n            : options.request;\n        const params = 'params' in options ? options.params : undefined;\n        const handler = new StrategyHandler(this, { event, request, params });\n        const responseDone = this._getResponse(handler, request, event);\n        const handlerDone = this._awaitComplete(responseDone, handler, request, event);\n        // Return an array of promises, suitable for use with Promise.all().\n        return [responseDone, handlerDone];\n    }\n    async _getResponse(handler, request, event) {\n        await handler.runCallbacks('handlerWillStart', { event, request });\n        let response = undefined;\n        try {\n            response = await this._handle(request, handler);\n            // The \"official\" Strategy subclasses all throw this error automatically,\n            // but in case a third-party Strategy doesn't, ensure that we have a\n            // consistent failure when there's no response or an error response.\n            if (!response || response.type === 'error') {\n                throw new WorkboxError('no-response', { url: request.url });\n            }\n        }\n        catch (error) {\n            if (error instanceof Error) {\n                for (const callback of handler.iterateCallbacks('handlerDidError')) {\n                    response = await callback({ error, event, request });\n                    if (response) {\n                        break;\n                    }\n                }\n            }\n            if (!response) {\n                throw error;\n            }\n            else if (process.env.NODE_ENV !== 'production') {\n                logger.log(`While responding to '${getFriendlyURL(request.url)}', ` +\n                    `an ${error instanceof Error ? error.toString() : ''} error occurred. Using a fallback response provided by ` +\n                    `a handlerDidError plugin.`);\n            }\n        }\n        for (const callback of handler.iterateCallbacks('handlerWillRespond')) {\n            response = await callback({ event, request, response });\n        }\n        return response;\n    }\n    async _awaitComplete(responseDone, handler, request, event) {\n        let response;\n        let error;\n        try {\n            response = await responseDone;\n        }\n        catch (error) {\n            // Ignore errors, as response errors should be caught via the `response`\n            // promise above. The `done` promise will only throw for errors in\n            // promises passed to `handler.waitUntil()`.\n        }\n        try {\n            await handler.runCallbacks('handlerDidRespond', {\n                event,\n                request,\n                response,\n            });\n            await handler.doneWaiting();\n        }\n        catch (waitUntilError) {\n            if (waitUntilError instanceof Error) {\n                error = waitUntilError;\n            }\n        }\n        await handler.runCallbacks('handlerDidComplete', {\n            event,\n            request,\n            response,\n            error: error,\n        });\n        handler.destroy();\n        if (error) {\n            throw error;\n        }\n    }\n}\nexport { Strategy };\n/**\n * Classes extending the `Strategy` based class should implement this method,\n * and leverage the {@link workbox-strategies.StrategyHandler}\n * arg to perform all fetching and cache logic, which will ensure all relevant\n * cache, cache options, fetch options and plugins are used (per the current\n * strategy instance).\n *\n * @name _handle\n * @instance\n * @abstract\n * @function\n * @param {Request} request\n * @param {workbox-strategies.StrategyHandler} handler\n * @return {Promise<Response>}\n *\n * @memberof workbox-strategies.Strategy\n */\n"], "mappings": ";;;;;;;;;;;;;;;;;AAEA,IAAI;AACA,OAAK,0BAA0B,KAAK,EAAE;AAC1C,SACO,GAAG;AAAE;;;ACWZ,SAAS,UAAU,OAAO;AACtB,SAAO,OAAO,UAAU,WAAW,IAAI,QAAQ,KAAK,IAAI;AAC5D;AAUA,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBlB,YAAY,UAAU,SAAS;AAC3B,SAAK,aAAa,CAAC;AAsCnB,QAAI,MAAuC;AACvC,yBAAO,WAAW,QAAQ,OAAO,iBAAiB;AAAA,QAC9C,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,WAAO,OAAO,MAAM,OAAO;AAC3B,SAAK,QAAQ,QAAQ;AACrB,SAAK,YAAY;AACjB,SAAK,mBAAmB,IAAI,SAAS;AACrC,SAAK,0BAA0B,CAAC;AAGhC,SAAK,WAAW,CAAC,GAAG,SAAS,OAAO;AACpC,SAAK,kBAAkB,oBAAI,IAAI;AAC/B,eAAW,UAAU,KAAK,UAAU;AAChC,WAAK,gBAAgB,IAAI,QAAQ,CAAC,CAAC;AAAA,IACvC;AACA,SAAK,MAAM,UAAU,KAAK,iBAAiB,OAAO;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,MAAM,OAAO;AACf,UAAM,EAAE,MAAM,IAAI;AAClB,QAAI,UAAU,UAAU,KAAK;AAC7B,QAAI,QAAQ,SAAS,cACjB,iBAAiB,cACjB,MAAM,iBAAiB;AACvB,YAAM,0BAA2B,MAAM,MAAM;AAC7C,UAAI,yBAAyB;AACzB,YAAI,MAAuC;AACvC,iBAAO,IAAI,8CACH,eAAe,QAAQ,GAAG,CAAC,GAAG;AAAA,QAC1C;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAIA,UAAM,kBAAkB,KAAK,YAAY,cAAc,IACjD,QAAQ,MAAM,IACd;AACN,QAAI;AACA,iBAAW,MAAM,KAAK,iBAAiB,kBAAkB,GAAG;AACxD,kBAAU,MAAM,GAAG,EAAE,SAAS,QAAQ,MAAM,GAAG,MAAM,CAAC;AAAA,MAC1D;AAAA,IACJ,SACO,KAAK;AACR,UAAI,eAAe,OAAO;AACtB,cAAM,IAAI,aAAa,mCAAmC;AAAA,UACtD,oBAAoB,IAAI;AAAA,QAC5B,CAAC;AAAA,MACL;AAAA,IACJ;AAIA,UAAM,wBAAwB,QAAQ,MAAM;AAC5C,QAAI;AACA,UAAI;AAEJ,sBAAgB,MAAM,MAAM,SAAS,QAAQ,SAAS,aAAa,SAAY,KAAK,UAAU,YAAY;AAC1G,UAAI,MAAuC;AACvC,eAAO,MAAM,wBACL,eAAe,QAAQ,GAAG,CAAC,sCACpB,cAAc,MAAM,IAAI;AAAA,MAC3C;AACA,iBAAW,YAAY,KAAK,iBAAiB,iBAAiB,GAAG;AAC7D,wBAAgB,MAAM,SAAS;AAAA,UAC3B;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,QACd,CAAC;AAAA,MACL;AACA,aAAO;AAAA,IACX,SACO,OAAO;AACV,UAAI,MAAuC;AACvC,eAAO,IAAI,wBACH,eAAe,QAAQ,GAAG,CAAC,qBAAqB,KAAK;AAAA,MACjE;AAGA,UAAI,iBAAiB;AACjB,cAAM,KAAK,aAAa,gBAAgB;AAAA,UACpC;AAAA,UACA;AAAA,UACA,iBAAiB,gBAAgB,MAAM;AAAA,UACvC,SAAS,sBAAsB,MAAM;AAAA,QACzC,CAAC;AAAA,MACL;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,iBAAiB,OAAO;AAC1B,UAAM,WAAW,MAAM,KAAK,MAAM,KAAK;AACvC,UAAM,gBAAgB,SAAS,MAAM;AACrC,SAAK,KAAK,UAAU,KAAK,SAAS,OAAO,aAAa,CAAC;AACvD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,WAAW,KAAK;AAClB,UAAM,UAAU,UAAU,GAAG;AAC7B,QAAI;AACJ,UAAM,EAAE,WAAW,aAAa,IAAI,KAAK;AACzC,UAAM,mBAAmB,MAAM,KAAK,YAAY,SAAS,MAAM;AAC/D,UAAM,oBAAoB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,YAAY,GAAG,EAAE,UAAU,CAAC;AACtF,qBAAiB,MAAM,OAAO,MAAM,kBAAkB,iBAAiB;AACvE,QAAI,MAAuC;AACvC,UAAI,gBAAgB;AAChB,eAAO,MAAM,+BAA+B,SAAS,IAAI;AAAA,MAC7D,OACK;AACD,eAAO,MAAM,gCAAgC,SAAS,IAAI;AAAA,MAC9D;AAAA,IACJ;AACA,eAAW,YAAY,KAAK,iBAAiB,0BAA0B,GAAG;AACtE,uBACK,MAAM,SAAS;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,OAAO,KAAK;AAAA,MAChB,CAAC,KAAM;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,SAAS,KAAK,UAAU;AAC1B,UAAM,UAAU,UAAU,GAAG;AAG7B,UAAM,QAAQ,CAAC;AACf,UAAM,mBAAmB,MAAM,KAAK,YAAY,SAAS,OAAO;AAChE,QAAI,MAAuC;AACvC,UAAI,iBAAiB,UAAU,iBAAiB,WAAW,OAAO;AAC9D,cAAM,IAAI,aAAa,oCAAoC;AAAA,UACvD,KAAK,eAAe,iBAAiB,GAAG;AAAA,UACxC,QAAQ,iBAAiB;AAAA,QAC7B,CAAC;AAAA,MACL;AAEA,YAAM,OAAO,SAAS,QAAQ,IAAI,MAAM;AACxC,UAAI,MAAM;AACN,eAAO,MAAM,oBAAoB,eAAe,iBAAiB,GAAG,CAAC,iBACjD,IAAI,oIAEsC;AAAA,MAClE;AAAA,IACJ;AACA,QAAI,CAAC,UAAU;AACX,UAAI,MAAuC;AACvC,eAAO,MAAM,2CACL,eAAe,iBAAiB,GAAG,CAAC,IAAI;AAAA,MACpD;AACA,YAAM,IAAI,aAAa,8BAA8B;AAAA,QACjD,KAAK,eAAe,iBAAiB,GAAG;AAAA,MAC5C,CAAC;AAAA,IACL;AACA,UAAM,kBAAkB,MAAM,KAAK,2BAA2B,QAAQ;AACtE,QAAI,CAAC,iBAAiB;AAClB,UAAI,MAAuC;AACvC,eAAO,MAAM,aAAa,eAAe,iBAAiB,GAAG,CAAC,yBACnC,eAAe;AAAA,MAC9C;AACA,aAAO;AAAA,IACX;AACA,UAAM,EAAE,WAAW,aAAa,IAAI,KAAK;AACzC,UAAM,QAAQ,MAAM,KAAK,OAAO,KAAK,SAAS;AAC9C,UAAM,yBAAyB,KAAK,YAAY,gBAAgB;AAChE,UAAM,cAAc,yBACd,MAAM;AAAA;AAAA;AAAA;AAAA,MAIR;AAAA,MAAO,iBAAiB,MAAM;AAAA,MAAG,CAAC,iBAAiB;AAAA,MAAG;AAAA,IAAY,IAChE;AACN,QAAI,MAAuC;AACvC,aAAO,MAAM,iBAAiB,SAAS,mCAC5B,eAAe,iBAAiB,GAAG,CAAC,GAAG;AAAA,IACtD;AACA,QAAI;AACA,YAAM,MAAM,IAAI,kBAAkB,yBAAyB,gBAAgB,MAAM,IAAI,eAAe;AAAA,IACxG,SACO,OAAO;AACV,UAAI,iBAAiB,OAAO;AAExB,YAAI,MAAM,SAAS,sBAAsB;AACrC,gBAAM,2BAA2B;AAAA,QACrC;AACA,cAAM;AAAA,MACV;AAAA,IACJ;AACA,eAAW,YAAY,KAAK,iBAAiB,gBAAgB,GAAG;AAC5D,YAAM,SAAS;AAAA,QACX;AAAA,QACA;AAAA,QACA,aAAa,gBAAgB,MAAM;AAAA,QACnC,SAAS;AAAA,QACT,OAAO,KAAK;AAAA,MAChB,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,YAAY,SAAS,MAAM;AAC7B,UAAM,MAAM,GAAG,QAAQ,GAAG,MAAM,IAAI;AACpC,QAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACvB,UAAI,mBAAmB;AACvB,iBAAW,YAAY,KAAK,iBAAiB,oBAAoB,GAAG;AAChE,2BAAmB,UAAU,MAAM,SAAS;AAAA,UACxC;AAAA,UACA,SAAS;AAAA,UACT,OAAO,KAAK;AAAA;AAAA,UAEZ,QAAQ,KAAK;AAAA;AAAA,QACjB,CAAC,CAAC;AAAA,MACN;AACA,WAAK,WAAW,GAAG,IAAI;AAAA,IAC3B;AACA,WAAO,KAAK,WAAW,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,MAAM;AACd,eAAW,UAAU,KAAK,UAAU,SAAS;AACzC,UAAI,QAAQ,QAAQ;AAChB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,MAAM,aAAa,MAAM,OAAO;AAC5B,eAAW,YAAY,KAAK,iBAAiB,IAAI,GAAG;AAGhD,YAAM,SAAS,KAAK;AAAA,IACxB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,CAAC,iBAAiB,MAAM;AACpB,eAAW,UAAU,KAAK,UAAU,SAAS;AACzC,UAAI,OAAO,OAAO,IAAI,MAAM,YAAY;AACpC,cAAM,QAAQ,KAAK,gBAAgB,IAAI,MAAM;AAC7C,cAAM,mBAAmB,CAAC,UAAU;AAChC,gBAAM,gBAAgB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,MAAM,CAAC;AAGvE,iBAAO,OAAO,IAAI,EAAE,aAAa;AAAA,QACrC;AACA,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,UAAU,SAAS;AACf,SAAK,wBAAwB,KAAK,OAAO;AACzC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,cAAc;AAChB,QAAI;AACJ,WAAQ,UAAU,KAAK,wBAAwB,MAAM,GAAI;AACrD,YAAM;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACN,SAAK,iBAAiB,QAAQ,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,2BAA2B,UAAU;AACvC,QAAI,kBAAkB;AACtB,QAAI,cAAc;AAClB,eAAW,YAAY,KAAK,iBAAiB,iBAAiB,GAAG;AAC7D,wBACK,MAAM,SAAS;AAAA,QACZ,SAAS,KAAK;AAAA,QACd,UAAU;AAAA,QACV,OAAO,KAAK;AAAA,MAChB,CAAC,KAAM;AACX,oBAAc;AACd,UAAI,CAAC,iBAAiB;AAClB;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,aAAa;AACd,UAAI,mBAAmB,gBAAgB,WAAW,KAAK;AACnD,0BAAkB;AAAA,MACtB;AACA,UAAI,MAAuC;AACvC,YAAI,iBAAiB;AACjB,cAAI,gBAAgB,WAAW,KAAK;AAChC,gBAAI,gBAAgB,WAAW,GAAG;AAC9B,qBAAO,KAAK,qBAAqB,KAAK,QAAQ,GAAG,6GAEM;AAAA,YAC3D,OACK;AACD,qBAAO,MAAM,qBAAqB,KAAK,QAAQ,GAAG,gCAChB,SAAS,MAAM,oCACrB;AAAA,YAChC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;;;ACjfA,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBX,YAAY,UAAU,CAAC,GAAG;AAQtB,SAAK,YAAY,WAAW,eAAe,QAAQ,SAAS;AAQ5D,SAAK,UAAU,QAAQ,WAAW,CAAC;AAQnC,SAAK,eAAe,QAAQ;AAQ5B,SAAK,eAAe,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,OAAO,SAAS;AACZ,UAAM,CAAC,YAAY,IAAI,KAAK,UAAU,OAAO;AAC7C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,UAAU,SAAS;AAEf,QAAI,mBAAmB,YAAY;AAC/B,gBAAU;AAAA,QACN,OAAO;AAAA,QACP,SAAS,QAAQ;AAAA,MACrB;AAAA,IACJ;AACA,UAAM,QAAQ,QAAQ;AACtB,UAAM,UAAU,OAAO,QAAQ,YAAY,WACrC,IAAI,QAAQ,QAAQ,OAAO,IAC3B,QAAQ;AACd,UAAM,SAAS,YAAY,UAAU,QAAQ,SAAS;AACtD,UAAM,UAAU,IAAI,gBAAgB,MAAM,EAAE,OAAO,SAAS,OAAO,CAAC;AACpE,UAAM,eAAe,KAAK,aAAa,SAAS,SAAS,KAAK;AAC9D,UAAM,cAAc,KAAK,eAAe,cAAc,SAAS,SAAS,KAAK;AAE7E,WAAO,CAAC,cAAc,WAAW;AAAA,EACrC;AAAA,EACA,MAAM,aAAa,SAAS,SAAS,OAAO;AACxC,UAAM,QAAQ,aAAa,oBAAoB,EAAE,OAAO,QAAQ,CAAC;AACjE,QAAI,WAAW;AACf,QAAI;AACA,iBAAW,MAAM,KAAK,QAAQ,SAAS,OAAO;AAI9C,UAAI,CAAC,YAAY,SAAS,SAAS,SAAS;AACxC,cAAM,IAAI,aAAa,eAAe,EAAE,KAAK,QAAQ,IAAI,CAAC;AAAA,MAC9D;AAAA,IACJ,SACO,OAAO;AACV,UAAI,iBAAiB,OAAO;AACxB,mBAAW,YAAY,QAAQ,iBAAiB,iBAAiB,GAAG;AAChE,qBAAW,MAAM,SAAS,EAAE,OAAO,OAAO,QAAQ,CAAC;AACnD,cAAI,UAAU;AACV;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,CAAC,UAAU;AACX,cAAM;AAAA,MACV,WACS,MAAuC;AAC5C,eAAO,IAAI,wBAAwB,eAAe,QAAQ,GAAG,CAAC,SACpD,iBAAiB,QAAQ,MAAM,SAAS,IAAI,EAAE,kFACzB;AAAA,MACnC;AAAA,IACJ;AACA,eAAW,YAAY,QAAQ,iBAAiB,oBAAoB,GAAG;AACnE,iBAAW,MAAM,SAAS,EAAE,OAAO,SAAS,SAAS,CAAC;AAAA,IAC1D;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM,eAAe,cAAc,SAAS,SAAS,OAAO;AACxD,QAAI;AACJ,QAAI;AACJ,QAAI;AACA,iBAAW,MAAM;AAAA,IACrB,SACOA,QAAO;AAAA,IAId;AACA,QAAI;AACA,YAAM,QAAQ,aAAa,qBAAqB;AAAA,QAC5C;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,YAAM,QAAQ,YAAY;AAAA,IAC9B,SACO,gBAAgB;AACnB,UAAI,0BAA0B,OAAO;AACjC,gBAAQ;AAAA,MACZ;AAAA,IACJ;AACA,UAAM,QAAQ,aAAa,sBAAsB;AAAA,MAC7C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,YAAQ,QAAQ;AAChB,QAAI,OAAO;AACP,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;", "names": ["error"]}