import {
  quotaErrorCallbacks
} from "./chunk-JC2IFURB.js";
import {
  finalAssertExports,
  logger
} from "./chunk-FWAXG7AD.js";

// node_modules/workbox-core/registerQuotaErrorCallback.js
function registerQuotaErrorCallback(callback) {
  if (true) {
    finalAssertExports.isType(callback, "function", {
      moduleName: "workbox-core",
      funcName: "register",
      paramName: "callback"
    });
  }
  quotaErrorCallbacks.add(callback);
  if (true) {
    logger.log("Registered a callback to respond to quota errors.", callback);
  }
}

// node_modules/workbox-core/_private/dontWaitFor.js
function dontWaitFor(promise) {
  void promise.then(() => {
  });
}

export {
  registerQuotaErrorCallback,
  dontWaitFor
};
//# sourceMappingURL=chunk-3TJHAT56.js.map
