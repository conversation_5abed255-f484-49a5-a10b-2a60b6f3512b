import {
  quotaErrorCallbacks
} from "./chunk-JC2IFURB.js";
import {
  logger
} from "./chunk-FWAXG7AD.js";

// node_modules/workbox-core/_private/cacheMatchIgnoreParams.js
function stripParams(fullURL, ignoreParams) {
  const strippedURL = new URL(fullURL);
  for (const param of ignoreParams) {
    strippedURL.searchParams.delete(param);
  }
  return strippedURL.href;
}
async function cacheMatchIgnoreParams(cache, request, ignoreParams, matchOptions) {
  const strippedRequestURL = stripParams(request.url, ignoreParams);
  if (request.url === strippedRequestURL) {
    return cache.match(request, matchOptions);
  }
  const keysOptions = Object.assign(Object.assign({}, matchOptions), { ignoreSearch: true });
  const cacheKeys = await cache.keys(request, keysOptions);
  for (const cacheKey of cacheKeys) {
    const strippedCacheKeyURL = stripParams(cacheKey.url, ignoreParams);
    if (strippedRequestURL === strippedCacheKeyURL) {
      return cache.match(cacheKey, matchOptions);
    }
  }
  return;
}

// node_modules/workbox-core/_private/Deferred.js
var Deferred = class {
  /**
   * Creates a promise and exposes its resolve and reject functions as methods.
   */
  constructor() {
    this.promise = new Promise((resolve, reject) => {
      this.resolve = resolve;
      this.reject = reject;
    });
  }
};

// node_modules/workbox-core/_private/executeQuotaErrorCallbacks.js
async function executeQuotaErrorCallbacks() {
  if (true) {
    logger.log(`About to run ${quotaErrorCallbacks.size} callbacks to clean up caches.`);
  }
  for (const callback of quotaErrorCallbacks) {
    await callback();
    if (true) {
      logger.log(callback, "is complete.");
    }
  }
  if (true) {
    logger.log("Finished running callbacks.");
  }
}

// node_modules/workbox-core/_private/timeout.js
function timeout(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export {
  cacheMatchIgnoreParams,
  Deferred,
  executeQuotaErrorCallbacks,
  timeout
};
//# sourceMappingURL=chunk-Y6342WCD.js.map
