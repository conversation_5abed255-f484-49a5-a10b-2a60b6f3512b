{"version": 3, "sources": ["../../workbox-core/_private/canConstructResponseFromBodyStream.js", "../../workbox-core/copyResponse.js", "../../workbox-core/_private/waitUntil.js"], "sourcesContent": ["/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nlet supportStatus;\n/**\n * A utility function that determines whether the current browser supports\n * constructing a new `Response` from a `response.body` stream.\n *\n * @return {boolean} `true`, if the current browser can successfully\n *     construct a `Response` from a `response.body` stream, `false` otherwise.\n *\n * @private\n */\nfunction canConstructResponseFromBodyStream() {\n    if (supportStatus === undefined) {\n        const testResponse = new Response('');\n        if ('body' in testResponse) {\n            try {\n                new Response(testResponse.body);\n                supportStatus = true;\n            }\n            catch (error) {\n                supportStatus = false;\n            }\n        }\n        supportStatus = false;\n    }\n    return supportStatus;\n}\nexport { canConstructResponseFromBodyStream };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { canConstructResponseFromBodyStream } from './_private/canConstructResponseFromBodyStream.js';\nimport { WorkboxError } from './_private/WorkboxError.js';\nimport './_version.js';\n/**\n * Allows developers to copy a response and modify its `headers`, `status`,\n * or `statusText` values (the values settable via a\n * [`ResponseInit`]{@link https://developer.mozilla.org/en-US/docs/Web/API/Response/Response#Syntax}\n * object in the constructor).\n * To modify these values, pass a function as the second argument. That\n * function will be invoked with a single object with the response properties\n * `{headers, status, statusText}`. The return value of this function will\n * be used as the `ResponseInit` for the new `Response`. To change the values\n * either modify the passed parameter(s) and return it, or return a totally\n * new object.\n *\n * This method is intentionally limited to same-origin responses, regardless of\n * whether CORS was used or not.\n *\n * @param {Response} response\n * @param {Function} modifier\n * @memberof workbox-core\n */\nasync function copyResponse(response, modifier) {\n    let origin = null;\n    // If response.url isn't set, assume it's cross-origin and keep origin null.\n    if (response.url) {\n        const responseURL = new URL(response.url);\n        origin = responseURL.origin;\n    }\n    if (origin !== self.location.origin) {\n        throw new WorkboxError('cross-origin-copy-response', { origin });\n    }\n    const clonedResponse = response.clone();\n    // Create a fresh `ResponseInit` object by cloning the headers.\n    const responseInit = {\n        headers: new Headers(clonedResponse.headers),\n        status: clonedResponse.status,\n        statusText: clonedResponse.statusText,\n    };\n    // Apply any user modifications.\n    const modifiedResponseInit = modifier ? modifier(responseInit) : responseInit;\n    // Create the new response from the body stream and `ResponseInit`\n    // modifications. Note: not all browsers support the Response.body stream,\n    // so fall back to reading the entire body into memory as a blob.\n    const body = canConstructResponseFromBodyStream()\n        ? clonedResponse.body\n        : await clonedResponse.blob();\n    return new Response(body, modifiedResponseInit);\n}\nexport { copyResponse };\n", "/*\n  Copyright 2020 Google LLC\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * A utility method that makes it easier to use `event.waitUntil` with\n * async functions and return the result.\n *\n * @param {ExtendableEvent} event\n * @param {Function} asyncFn\n * @return {Function}\n * @private\n */\nfunction waitUntil(event, asyncFn) {\n    const returnPromise = asyncFn();\n    event.waitUntil(returnPromise);\n    return returnPromise;\n}\nexport { waitUntil };\n"], "mappings": ";;;;;AAQA,IAAI;AAUJ,SAAS,qCAAqC;AAC1C,MAAI,kBAAkB,QAAW;AAC7B,UAAM,eAAe,IAAI,SAAS,EAAE;AACpC,QAAI,UAAU,cAAc;AACxB,UAAI;AACA,YAAI,SAAS,aAAa,IAAI;AAC9B,wBAAgB;AAAA,MACpB,SACO,OAAO;AACV,wBAAgB;AAAA,MACpB;AAAA,IACJ;AACA,oBAAgB;AAAA,EACpB;AACA,SAAO;AACX;;;ACJA,eAAe,aAAa,UAAU,UAAU;AAC5C,MAAI,SAAS;AAEb,MAAI,SAAS,KAAK;AACd,UAAM,cAAc,IAAI,IAAI,SAAS,GAAG;AACxC,aAAS,YAAY;AAAA,EACzB;AACA,MAAI,WAAW,KAAK,SAAS,QAAQ;AACjC,UAAM,IAAI,aAAa,8BAA8B,EAAE,OAAO,CAAC;AAAA,EACnE;AACA,QAAM,iBAAiB,SAAS,MAAM;AAEtC,QAAM,eAAe;AAAA,IACjB,SAAS,IAAI,QAAQ,eAAe,OAAO;AAAA,IAC3C,QAAQ,eAAe;AAAA,IACvB,YAAY,eAAe;AAAA,EAC/B;AAEA,QAAM,uBAAuB,WAAW,SAAS,YAAY,IAAI;AAIjE,QAAM,OAAO,mCAAmC,IAC1C,eAAe,OACf,MAAM,eAAe,KAAK;AAChC,SAAO,IAAI,SAAS,MAAM,oBAAoB;AAClD;;;ACvCA,SAAS,UAAU,OAAO,SAAS;AAC/B,QAAM,gBAAgB,QAAQ;AAC9B,QAAM,UAAU,aAAa;AAC7B,SAAO;AACX;", "names": []}