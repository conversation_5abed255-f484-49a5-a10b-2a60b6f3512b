{"version": 3, "sources": ["../../workbox-core/models/quotaErrorCallbacks.js", "../../workbox-core/_private/cacheNames.js"], "sourcesContent": ["/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n// Callbacks to be executed whenever there's a quota error.\n// Can't change Function type right now.\n// eslint-disable-next-line @typescript-eslint/ban-types\nconst quotaErrorCallbacks = new Set();\nexport { quotaErrorCallbacks };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\nconst _cacheNameDetails = {\n    googleAnalytics: 'googleAnalytics',\n    precache: 'precache-v2',\n    prefix: 'workbox',\n    runtime: 'runtime',\n    suffix: typeof registration !== 'undefined' ? registration.scope : '',\n};\nconst _createCacheName = (cacheName) => {\n    return [_cacheNameDetails.prefix, cacheName, _cacheNameDetails.suffix]\n        .filter((value) => value && value.length > 0)\n        .join('-');\n};\nconst eachCacheNameDetail = (fn) => {\n    for (const key of Object.keys(_cacheNameDetails)) {\n        fn(key);\n    }\n};\nexport const cacheNames = {\n    updateDetails: (details) => {\n        eachCacheNameDetail((key) => {\n            if (typeof details[key] === 'string') {\n                _cacheNameDetails[key] = details[key];\n            }\n        });\n    },\n    getGoogleAnalyticsName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.googleAnalytics);\n    },\n    getPrecacheName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.precache);\n    },\n    getPrefix: () => {\n        return _cacheNameDetails.prefix;\n    },\n    getRuntimeName: (userCacheName) => {\n        return userCacheName || _createCacheName(_cacheNameDetails.runtime);\n    },\n    getSuffix: () => {\n        return _cacheNameDetails.suffix;\n    },\n};\n"], "mappings": ";AAWA,IAAM,sBAAsB,oBAAI,IAAI;;;ACHpC,IAAM,oBAAoB;AAAA,EACtB,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ,OAAO,iBAAiB,cAAc,aAAa,QAAQ;AACvE;AACA,IAAM,mBAAmB,CAAC,cAAc;AACpC,SAAO,CAAC,kBAAkB,QAAQ,WAAW,kBAAkB,MAAM,EAChE,OAAO,CAAC,UAAU,SAAS,MAAM,SAAS,CAAC,EAC3C,KAAK,GAAG;AACjB;AACA,IAAM,sBAAsB,CAAC,OAAO;AAChC,aAAW,OAAO,OAAO,KAAK,iBAAiB,GAAG;AAC9C,OAAG,GAAG;AAAA,EACV;AACJ;AACO,IAAM,aAAa;AAAA,EACtB,eAAe,CAAC,YAAY;AACxB,wBAAoB,CAAC,QAAQ;AACzB,UAAI,OAAO,QAAQ,GAAG,MAAM,UAAU;AAClC,0BAAkB,GAAG,IAAI,QAAQ,GAAG;AAAA,MACxC;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EACA,wBAAwB,CAAC,kBAAkB;AACvC,WAAO,iBAAiB,iBAAiB,kBAAkB,eAAe;AAAA,EAC9E;AAAA,EACA,iBAAiB,CAAC,kBAAkB;AAChC,WAAO,iBAAiB,iBAAiB,kBAAkB,QAAQ;AAAA,EACvE;AAAA,EACA,WAAW,MAAM;AACb,WAAO,kBAAkB;AAAA,EAC7B;AAAA,EACA,gBAAgB,CAAC,kBAAkB;AAC/B,WAAO,iBAAiB,iBAAiB,kBAAkB,OAAO;AAAA,EACtE;AAAA,EACA,WAAW,MAAM;AACb,WAAO,kBAAkB;AAAA,EAC7B;AACJ;", "names": []}