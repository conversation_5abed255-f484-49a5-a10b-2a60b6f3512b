{"version": 3, "sources": ["../../workbox-routing/_version.js", "../../workbox-routing/utils/constants.js", "../../workbox-routing/utils/normalizeHandler.js", "../../workbox-routing/Route.js", "../../workbox-routing/RegExpRoute.js", "../../workbox-routing/Router.js", "../../workbox-routing/utils/getOrCreateDefaultRouter.js", "../../workbox-routing/registerRoute.js"], "sourcesContent": ["\"use strict\";\n// @ts-ignore\ntry {\n    self['workbox:routing:7.2.0'] && _();\n}\ncatch (e) { }\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport '../_version.js';\n/**\n * The default HTTP method, 'GET', used when there's no specific method\n * configured for a route.\n *\n * @type {string}\n *\n * @private\n */\nexport const defaultMethod = 'GET';\n/**\n * The list of valid HTTP methods associated with requests that could be routed.\n *\n * @type {Array<string>}\n *\n * @private\n */\nexport const validMethods = [\n    'DELETE',\n    'GET',\n    'HEAD',\n    'PATCH',\n    'POST',\n    'PUT',\n];\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport '../_version.js';\n/**\n * @param {function()|Object} handler Either a function, or an object with a\n * 'handle' method.\n * @return {Object} An object with a handle method.\n *\n * @private\n */\nexport const normalizeHandler = (handler) => {\n    if (handler && typeof handler === 'object') {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.hasMethod(handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return handler;\n    }\n    else {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(handler, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'handler',\n            });\n        }\n        return { handle: handler };\n    }\n};\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { defaultMethod, validMethods } from './utils/constants.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport './_version.js';\n/**\n * A `Route` consists of a pair of callback functions, \"match\" and \"handler\".\n * The \"match\" callback determine if a route should be used to \"handle\" a\n * request by returning a non-falsy value if it can. The \"handler\" callback\n * is called when there is a match and should return a Promise that resolves\n * to a `Response`.\n *\n * @memberof workbox-routing\n */\nclass Route {\n    /**\n     * Constructor for Route class.\n     *\n     * @param {workbox-routing~matchCallback} match\n     * A callback function that determines whether the route matches a given\n     * `fetch` event by returning a non-falsy value.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(match, handler, method = defaultMethod) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(match, 'function', {\n                moduleName: 'workbox-routing',\n                className: 'Route',\n                funcName: 'constructor',\n                paramName: 'match',\n            });\n            if (method) {\n                assert.isOneOf(method, validMethods, { paramName: 'method' });\n            }\n        }\n        // These values are referenced directly by Router so cannot be\n        // altered by minificaton.\n        this.handler = normalizeHandler(handler);\n        this.match = match;\n        this.method = method;\n    }\n    /**\n     *\n     * @param {workbox-routing-handlerCallback} handler A callback\n     * function that returns a Promise resolving to a Response\n     */\n    setCatchHandler(handler) {\n        this.catchHandler = normalizeHandler(handler);\n    }\n}\nexport { Route };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { Route } from './Route.js';\nimport './_version.js';\n/**\n * RegExpRoute makes it easy to create a regular expression based\n * {@link workbox-routing.Route}.\n *\n * For same-origin requests the RegExp only needs to match part of the URL. For\n * requests against third-party servers, you must define a RegExp that matches\n * the start of the URL.\n *\n * @memberof workbox-routing\n * @extends workbox-routing.Route\n */\nclass RegExpRoute extends Route {\n    /**\n     * If the regular expression contains\n     * [capture groups]{@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/RegExp#grouping-back-references},\n     * the captured values will be passed to the\n     * {@link workbox-routing~handlerCallback} `params`\n     * argument.\n     *\n     * @param {RegExp} regExp The regular expression to match against URLs.\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to match the Route\n     * against.\n     */\n    constructor(regExp, handler, method) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(regExp, RegExp, {\n                moduleName: 'workbox-routing',\n                className: 'RegExpRoute',\n                funcName: 'constructor',\n                paramName: 'pattern',\n            });\n        }\n        const match = ({ url }) => {\n            const result = regExp.exec(url.href);\n            // Return immediately if there's no match.\n            if (!result) {\n                return;\n            }\n            // Require that the match start at the first character in the URL string\n            // if it's a cross-origin request.\n            // See https://github.com/GoogleChrome/workbox/issues/281 for the context\n            // behind this behavior.\n            if (url.origin !== location.origin && result.index !== 0) {\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`The regular expression '${regExp.toString()}' only partially matched ` +\n                        `against the cross-origin URL '${url.toString()}'. RegExpRoute's will only ` +\n                        `handle cross-origin requests if they match the entire URL.`);\n                }\n                return;\n            }\n            // If the route matches, but there aren't any capture groups defined, then\n            // this will return [], which is truthy and therefore sufficient to\n            // indicate a match.\n            // If there are capture groups, then it will return their values.\n            return result.slice(1);\n        };\n        super(match, handler, method);\n    }\n}\nexport { RegExpRoute };\n", "/*\n  Copyright 2018 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { assert } from 'workbox-core/_private/assert.js';\nimport { getFriendlyURL } from 'workbox-core/_private/getFriendlyURL.js';\nimport { defaultMethod } from './utils/constants.js';\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { normalizeHandler } from './utils/normalizeHandler.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport './_version.js';\n/**\n * The Router can be used to process a `FetchEvent` using one or more\n * {@link workbox-routing.Route}, responding with a `Response` if\n * a matching route exists.\n *\n * If no route matches a given a request, the Router will use a \"default\"\n * handler if one is defined.\n *\n * Should the matching Route throw an error, the Router will use a \"catch\"\n * handler if one is defined to gracefully deal with issues and respond with a\n * Request.\n *\n * If a request matches multiple routes, the **earliest** registered route will\n * be used to respond to the request.\n *\n * @memberof workbox-routing\n */\nclass Router {\n    /**\n     * Initializes a new Router.\n     */\n    constructor() {\n        this._routes = new Map();\n        this._defaultHandlerMap = new Map();\n    }\n    /**\n     * @return {Map<string, Array<workbox-routing.Route>>} routes A `Map` of HTTP\n     * method name ('GET', etc.) to an array of all the corresponding `Route`\n     * instances that are registered.\n     */\n    get routes() {\n        return this._routes;\n    }\n    /**\n     * Adds a fetch event listener to respond to events when a route matches\n     * the event's request.\n     */\n    addFetchListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('fetch', ((event) => {\n            const { request } = event;\n            const responsePromise = this.handleRequest({ request, event });\n            if (responsePromise) {\n                event.respondWith(responsePromise);\n            }\n        }));\n    }\n    /**\n     * Adds a message event listener for URLs to cache from the window.\n     * This is useful to cache resources loaded on the page prior to when the\n     * service worker started controlling it.\n     *\n     * The format of the message data sent from the window should be as follows.\n     * Where the `urlsToCache` array may consist of URL strings or an array of\n     * URL string + `requestInit` object (the same as you'd pass to `fetch()`).\n     *\n     * ```\n     * {\n     *   type: 'CACHE_URLS',\n     *   payload: {\n     *     urlsToCache: [\n     *       './script1.js',\n     *       './script2.js',\n     *       ['./script3.js', {mode: 'no-cors'}],\n     *     ],\n     *   },\n     * }\n     * ```\n     */\n    addCacheListener() {\n        // See https://github.com/Microsoft/TypeScript/issues/28357#issuecomment-*********\n        self.addEventListener('message', ((event) => {\n            // event.data is type 'any'\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            if (event.data && event.data.type === 'CACHE_URLS') {\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                const { payload } = event.data;\n                if (process.env.NODE_ENV !== 'production') {\n                    logger.debug(`Caching URLs from the window`, payload.urlsToCache);\n                }\n                const requestPromises = Promise.all(payload.urlsToCache.map((entry) => {\n                    if (typeof entry === 'string') {\n                        entry = [entry];\n                    }\n                    const request = new Request(...entry);\n                    return this.handleRequest({ request, event });\n                    // TODO(philipwalton): TypeScript errors without this typecast for\n                    // some reason (probably a bug). The real type here should work but\n                    // doesn't: `Array<Promise<Response> | undefined>`.\n                })); // TypeScript\n                event.waitUntil(requestPromises);\n                // If a MessageChannel was used, reply to the message on success.\n                if (event.ports && event.ports[0]) {\n                    void requestPromises.then(() => event.ports[0].postMessage(true));\n                }\n            }\n        }));\n    }\n    /**\n     * Apply the routing rules to a FetchEvent object to get a Response from an\n     * appropriate Route's handler.\n     *\n     * @param {Object} options\n     * @param {Request} options.request The request to handle.\n     * @param {ExtendableEvent} options.event The event that triggered the\n     *     request.\n     * @return {Promise<Response>|undefined} A promise is returned if a\n     *     registered route can handle the request. If there is no matching\n     *     route and there's no `defaultHandler`, `undefined` is returned.\n     */\n    handleRequest({ request, event, }) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isInstance(request, Request, {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'handleRequest',\n                paramName: 'options.request',\n            });\n        }\n        const url = new URL(request.url, location.href);\n        if (!url.protocol.startsWith('http')) {\n            if (process.env.NODE_ENV !== 'production') {\n                logger.debug(`Workbox Router only supports URLs that start with 'http'.`);\n            }\n            return;\n        }\n        const sameOrigin = url.origin === location.origin;\n        const { params, route } = this.findMatchingRoute({\n            event,\n            request,\n            sameOrigin,\n            url,\n        });\n        let handler = route && route.handler;\n        const debugMessages = [];\n        if (process.env.NODE_ENV !== 'production') {\n            if (handler) {\n                debugMessages.push([`Found a route to handle this request:`, route]);\n                if (params) {\n                    debugMessages.push([\n                        `Passing the following params to the route's handler:`,\n                        params,\n                    ]);\n                }\n            }\n        }\n        // If we don't have a handler because there was no matching route, then\n        // fall back to defaultHandler if that's defined.\n        const method = request.method;\n        if (!handler && this._defaultHandlerMap.has(method)) {\n            if (process.env.NODE_ENV !== 'production') {\n                debugMessages.push(`Failed to find a matching route. Falling ` +\n                    `back to the default handler for ${method}.`);\n            }\n            handler = this._defaultHandlerMap.get(method);\n        }\n        if (!handler) {\n            if (process.env.NODE_ENV !== 'production') {\n                // No handler so Workbox will do nothing. If logs is set of debug\n                // i.e. verbose, we should print out this information.\n                logger.debug(`No route found for: ${getFriendlyURL(url)}`);\n            }\n            return;\n        }\n        if (process.env.NODE_ENV !== 'production') {\n            // We have a handler, meaning Workbox is going to handle the route.\n            // print the routing details to the console.\n            logger.groupCollapsed(`Router is responding to: ${getFriendlyURL(url)}`);\n            debugMessages.forEach((msg) => {\n                if (Array.isArray(msg)) {\n                    logger.log(...msg);\n                }\n                else {\n                    logger.log(msg);\n                }\n            });\n            logger.groupEnd();\n        }\n        // Wrap in try and catch in case the handle method throws a synchronous\n        // error. It should still callback to the catch handler.\n        let responsePromise;\n        try {\n            responsePromise = handler.handle({ url, request, event, params });\n        }\n        catch (err) {\n            responsePromise = Promise.reject(err);\n        }\n        // Get route's catch handler, if it exists\n        const catchHandler = route && route.catchHandler;\n        if (responsePromise instanceof Promise &&\n            (this._catchHandler || catchHandler)) {\n            responsePromise = responsePromise.catch(async (err) => {\n                // If there's a route catch handler, process that first\n                if (catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to route's Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    try {\n                        return await catchHandler.handle({ url, request, event, params });\n                    }\n                    catch (catchErr) {\n                        if (catchErr instanceof Error) {\n                            err = catchErr;\n                        }\n                    }\n                }\n                if (this._catchHandler) {\n                    if (process.env.NODE_ENV !== 'production') {\n                        // Still include URL here as it will be async from the console group\n                        // and may not make sense without the URL\n                        logger.groupCollapsed(`Error thrown when responding to: ` +\n                            ` ${getFriendlyURL(url)}. Falling back to global Catch Handler.`);\n                        logger.error(`Error thrown by:`, route);\n                        logger.error(err);\n                        logger.groupEnd();\n                    }\n                    return this._catchHandler.handle({ url, request, event });\n                }\n                throw err;\n            });\n        }\n        return responsePromise;\n    }\n    /**\n     * Checks a request and URL (and optionally an event) against the list of\n     * registered routes, and if there's a match, returns the corresponding\n     * route along with any params generated by the match.\n     *\n     * @param {Object} options\n     * @param {URL} options.url\n     * @param {boolean} options.sameOrigin The result of comparing `url.origin`\n     *     against the current origin.\n     * @param {Request} options.request The request to match.\n     * @param {Event} options.event The corresponding event.\n     * @return {Object} An object with `route` and `params` properties.\n     *     They are populated if a matching route was found or `undefined`\n     *     otherwise.\n     */\n    findMatchingRoute({ url, sameOrigin, request, event, }) {\n        const routes = this._routes.get(request.method) || [];\n        for (const route of routes) {\n            let params;\n            // route.match returns type any, not possible to change right now.\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            const matchResult = route.match({ url, sameOrigin, request, event });\n            if (matchResult) {\n                if (process.env.NODE_ENV !== 'production') {\n                    // Warn developers that using an async matchCallback is almost always\n                    // not the right thing to do.\n                    if (matchResult instanceof Promise) {\n                        logger.warn(`While routing ${getFriendlyURL(url)}, an async ` +\n                            `matchCallback function was used. Please convert the ` +\n                            `following route to use a synchronous matchCallback function:`, route);\n                    }\n                }\n                // See https://github.com/GoogleChrome/workbox/issues/2079\n                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                params = matchResult;\n                if (Array.isArray(params) && params.length === 0) {\n                    // Instead of passing an empty array in as params, use undefined.\n                    params = undefined;\n                }\n                else if (matchResult.constructor === Object && // eslint-disable-line\n                    Object.keys(matchResult).length === 0) {\n                    // Instead of passing an empty object in as params, use undefined.\n                    params = undefined;\n                }\n                else if (typeof matchResult === 'boolean') {\n                    // For the boolean value true (rather than just something truth-y),\n                    // don't set params.\n                    // See https://github.com/GoogleChrome/workbox/pull/2134#issuecomment-513924353\n                    params = undefined;\n                }\n                // Return early if have a match.\n                return { route, params };\n            }\n        }\n        // If no match was found above, return and empty object.\n        return {};\n    }\n    /**\n     * Define a default `handler` that's called when no routes explicitly\n     * match the incoming request.\n     *\n     * Each HTTP method ('GET', 'POST', etc.) gets its own default handler.\n     *\n     * Without a default handler, unmatched requests will go against the\n     * network as if there were no service worker present.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     * @param {string} [method='GET'] The HTTP method to associate with this\n     * default handler. Each method has its own default.\n     */\n    setDefaultHandler(handler, method = defaultMethod) {\n        this._defaultHandlerMap.set(method, normalizeHandler(handler));\n    }\n    /**\n     * If a Route throws an error while handling a request, this `handler`\n     * will be called and given a chance to provide a response.\n     *\n     * @param {workbox-routing~handlerCallback} handler A callback\n     * function that returns a Promise resulting in a Response.\n     */\n    setCatchHandler(handler) {\n        this._catchHandler = normalizeHandler(handler);\n    }\n    /**\n     * Registers a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to register.\n     */\n    registerRoute(route) {\n        if (process.env.NODE_ENV !== 'production') {\n            assert.isType(route, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route, 'match', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.isType(route.handler, 'object', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route',\n            });\n            assert.hasMethod(route.handler, 'handle', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.handler',\n            });\n            assert.isType(route.method, 'string', {\n                moduleName: 'workbox-routing',\n                className: 'Router',\n                funcName: 'registerRoute',\n                paramName: 'route.method',\n            });\n        }\n        if (!this._routes.has(route.method)) {\n            this._routes.set(route.method, []);\n        }\n        // Give precedence to all of the earlier routes by adding this additional\n        // route to the end of the array.\n        this._routes.get(route.method).push(route);\n    }\n    /**\n     * Unregisters a route with the router.\n     *\n     * @param {workbox-routing.Route} route The route to unregister.\n     */\n    unregisterRoute(route) {\n        if (!this._routes.has(route.method)) {\n            throw new WorkboxError('unregister-route-but-not-found-with-method', {\n                method: route.method,\n            });\n        }\n        const routeIndex = this._routes.get(route.method).indexOf(route);\n        if (routeIndex > -1) {\n            this._routes.get(route.method).splice(routeIndex, 1);\n        }\n        else {\n            throw new WorkboxError('unregister-route-route-not-registered');\n        }\n    }\n}\nexport { Router };\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { Router } from '../Router.js';\nimport '../_version.js';\nlet defaultRouter;\n/**\n * Creates a new, singleton Router instance if one does not exist. If one\n * does already exist, that instance is returned.\n *\n * @private\n * @return {Router}\n */\nexport const getOrCreateDefaultRouter = () => {\n    if (!defaultRouter) {\n        defaultRouter = new Router();\n        // The helpers that use the default Router assume these listeners exist.\n        defaultRouter.addFetchListener();\n        defaultRouter.addCacheListener();\n    }\n    return defaultRouter;\n};\n", "/*\n  Copyright 2019 Google LLC\n\n  Use of this source code is governed by an MIT-style\n  license that can be found in the LICENSE file or at\n  https://opensource.org/licenses/MIT.\n*/\nimport { logger } from 'workbox-core/_private/logger.js';\nimport { WorkboxError } from 'workbox-core/_private/WorkboxError.js';\nimport { Route } from './Route.js';\nimport { RegExpRoute } from './RegExpRoute.js';\nimport { getOrCreateDefaultRouter } from './utils/getOrCreateDefaultRouter.js';\nimport './_version.js';\n/**\n * Easily register a RegExp, string, or function with a caching\n * strategy to a singleton Router instance.\n *\n * This method will generate a Route for you if needed and\n * call {@link workbox-routing.Router#registerRoute}.\n *\n * @param {RegExp|string|workbox-routing.Route~matchCallback|workbox-routing.Route} capture\n * If the capture param is a `Route`, all other arguments will be ignored.\n * @param {workbox-routing~handlerCallback} [handler] A callback\n * function that returns a Promise resulting in a Response. This parameter\n * is required if `capture` is not a `Route` object.\n * @param {string} [method='GET'] The HTTP method to match the Route\n * against.\n * @return {workbox-routing.Route} The generated `Route`.\n *\n * @memberof workbox-routing\n */\nfunction registerRoute(capture, handler, method) {\n    let route;\n    if (typeof capture === 'string') {\n        const captureUrl = new URL(capture, location.href);\n        if (process.env.NODE_ENV !== 'production') {\n            if (!(capture.startsWith('/') || capture.startsWith('http'))) {\n                throw new WorkboxError('invalid-string', {\n                    moduleName: 'workbox-routing',\n                    funcName: 'registerRoute',\n                    paramName: 'capture',\n                });\n            }\n            // We want to check if Express-style wildcards are in the pathname only.\n            // TODO: Remove this log message in v4.\n            const valueToCheck = capture.startsWith('http')\n                ? captureUrl.pathname\n                : capture;\n            // See https://github.com/pillarjs/path-to-regexp#parameters\n            const wildcards = '[*:?+]';\n            if (new RegExp(`${wildcards}`).exec(valueToCheck)) {\n                logger.debug(`The '$capture' parameter contains an Express-style wildcard ` +\n                    `character (${wildcards}). Strings are now always interpreted as ` +\n                    `exact matches; use a RegExp for partial or wildcard matches.`);\n            }\n        }\n        const matchCallback = ({ url }) => {\n            if (process.env.NODE_ENV !== 'production') {\n                if (url.pathname === captureUrl.pathname &&\n                    url.origin !== captureUrl.origin) {\n                    logger.debug(`${capture} only partially matches the cross-origin URL ` +\n                        `${url.toString()}. This route will only handle cross-origin requests ` +\n                        `if they match the entire URL.`);\n                }\n            }\n            return url.href === captureUrl.href;\n        };\n        // If `capture` is a string then `handler` and `method` must be present.\n        route = new Route(matchCallback, handler, method);\n    }\n    else if (capture instanceof RegExp) {\n        // If `capture` is a `RegExp` then `handler` and `method` must be present.\n        route = new RegExpRoute(capture, handler, method);\n    }\n    else if (typeof capture === 'function') {\n        // If `capture` is a function then `handler` and `method` must be present.\n        route = new Route(capture, handler, method);\n    }\n    else if (capture instanceof Route) {\n        route = capture;\n    }\n    else {\n        throw new WorkboxError('unsupported-route-type', {\n            moduleName: 'workbox-routing',\n            funcName: 'registerRoute',\n            paramName: 'capture',\n        });\n    }\n    const defaultRouter = getOrCreateDefaultRouter();\n    defaultRouter.registerRoute(route);\n    return route;\n}\nexport { registerRoute };\n"], "mappings": ";;;;;;;;AAEA,IAAI;AACA,OAAK,uBAAuB,KAAK,EAAE;AACvC,SACO,GAAG;AAAE;;;ACWL,IAAM,gBAAgB;AAQtB,IAAM,eAAe;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;ACfO,IAAM,mBAAmB,CAAC,YAAY;AACzC,MAAI,WAAW,OAAO,YAAY,UAAU;AACxC,QAAI,MAAuC;AACvC,yBAAO,UAAU,SAAS,UAAU;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX,OACK;AACD,QAAI,MAAuC;AACvC,yBAAO,OAAO,SAAS,YAAY;AAAA,QAC/B,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,WAAO,EAAE,QAAQ,QAAQ;AAAA,EAC7B;AACJ;;;ACnBA,IAAM,QAAN,MAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYR,YAAY,OAAO,SAAS,SAAS,eAAe;AAChD,QAAI,MAAuC;AACvC,yBAAO,OAAO,OAAO,YAAY;AAAA,QAC7B,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AACD,UAAI,QAAQ;AACR,2BAAO,QAAQ,QAAQ,cAAc,EAAE,WAAW,SAAS,CAAC;AAAA,MAChE;AAAA,IACJ;AAGA,SAAK,UAAU,iBAAiB,OAAO;AACvC,SAAK,QAAQ;AACb,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,SAAS;AACrB,SAAK,eAAe,iBAAiB,OAAO;AAAA,EAChD;AACJ;;;ACpCA,IAAM,cAAN,cAA0B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAc5B,YAAY,QAAQ,SAAS,QAAQ;AACjC,QAAI,MAAuC;AACvC,yBAAO,WAAW,QAAQ,QAAQ;AAAA,QAC9B,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,QAAQ,CAAC,EAAE,IAAI,MAAM;AACvB,YAAM,SAAS,OAAO,KAAK,IAAI,IAAI;AAEnC,UAAI,CAAC,QAAQ;AACT;AAAA,MACJ;AAKA,UAAI,IAAI,WAAW,SAAS,UAAU,OAAO,UAAU,GAAG;AACtD,YAAI,MAAuC;AACvC,iBAAO,MAAM,2BAA2B,OAAO,SAAS,CAAC,0DACpB,IAAI,SAAS,CAAC,uFACa;AAAA,QACpE;AACA;AAAA,MACJ;AAKA,aAAO,OAAO,MAAM,CAAC;AAAA,IACzB;AACA,UAAM,OAAO,SAAS,MAAM;AAAA,EAChC;AACJ;;;ACxCA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIT,cAAc;AACV,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,qBAAqB,oBAAI,IAAI;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAEf,SAAK,iBAAiB,SAAU,CAAC,UAAU;AACvC,YAAM,EAAE,QAAQ,IAAI;AACpB,YAAM,kBAAkB,KAAK,cAAc,EAAE,SAAS,MAAM,CAAC;AAC7D,UAAI,iBAAiB;AACjB,cAAM,YAAY,eAAe;AAAA,MACrC;AAAA,IACJ,CAAE;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBA,mBAAmB;AAEf,SAAK,iBAAiB,WAAY,CAAC,UAAU;AAGzC,UAAI,MAAM,QAAQ,MAAM,KAAK,SAAS,cAAc;AAEhD,cAAM,EAAE,QAAQ,IAAI,MAAM;AAC1B,YAAI,MAAuC;AACvC,iBAAO,MAAM,gCAAgC,QAAQ,WAAW;AAAA,QACpE;AACA,cAAM,kBAAkB,QAAQ,IAAI,QAAQ,YAAY,IAAI,CAAC,UAAU;AACnE,cAAI,OAAO,UAAU,UAAU;AAC3B,oBAAQ,CAAC,KAAK;AAAA,UAClB;AACA,gBAAM,UAAU,IAAI,QAAQ,GAAG,KAAK;AACpC,iBAAO,KAAK,cAAc,EAAE,SAAS,MAAM,CAAC;AAAA,QAIhD,CAAC,CAAC;AACF,cAAM,UAAU,eAAe;AAE/B,YAAI,MAAM,SAAS,MAAM,MAAM,CAAC,GAAG;AAC/B,eAAK,gBAAgB,KAAK,MAAM,MAAM,MAAM,CAAC,EAAE,YAAY,IAAI,CAAC;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ,CAAE;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,cAAc,EAAE,SAAS,MAAO,GAAG;AAC/B,QAAI,MAAuC;AACvC,yBAAO,WAAW,SAAS,SAAS;AAAA,QAChC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,UAAM,MAAM,IAAI,IAAI,QAAQ,KAAK,SAAS,IAAI;AAC9C,QAAI,CAAC,IAAI,SAAS,WAAW,MAAM,GAAG;AAClC,UAAI,MAAuC;AACvC,eAAO,MAAM,2DAA2D;AAAA,MAC5E;AACA;AAAA,IACJ;AACA,UAAM,aAAa,IAAI,WAAW,SAAS;AAC3C,UAAM,EAAE,QAAQ,MAAM,IAAI,KAAK,kBAAkB;AAAA,MAC7C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,UAAU,SAAS,MAAM;AAC7B,UAAM,gBAAgB,CAAC;AACvB,QAAI,MAAuC;AACvC,UAAI,SAAS;AACT,sBAAc,KAAK,CAAC,yCAAyC,KAAK,CAAC;AACnE,YAAI,QAAQ;AACR,wBAAc,KAAK;AAAA,YACf;AAAA,YACA;AAAA,UACJ,CAAC;AAAA,QACL;AAAA,MACJ;AAAA,IACJ;AAGA,UAAM,SAAS,QAAQ;AACvB,QAAI,CAAC,WAAW,KAAK,mBAAmB,IAAI,MAAM,GAAG;AACjD,UAAI,MAAuC;AACvC,sBAAc,KAAK,4EACoB,MAAM,GAAG;AAAA,MACpD;AACA,gBAAU,KAAK,mBAAmB,IAAI,MAAM;AAAA,IAChD;AACA,QAAI,CAAC,SAAS;AACV,UAAI,MAAuC;AAGvC,eAAO,MAAM,uBAAuB,eAAe,GAAG,CAAC,EAAE;AAAA,MAC7D;AACA;AAAA,IACJ;AACA,QAAI,MAAuC;AAGvC,aAAO,eAAe,4BAA4B,eAAe,GAAG,CAAC,EAAE;AACvE,oBAAc,QAAQ,CAAC,QAAQ;AAC3B,YAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,iBAAO,IAAI,GAAG,GAAG;AAAA,QACrB,OACK;AACD,iBAAO,IAAI,GAAG;AAAA,QAClB;AAAA,MACJ,CAAC;AACD,aAAO,SAAS;AAAA,IACpB;AAGA,QAAI;AACJ,QAAI;AACA,wBAAkB,QAAQ,OAAO,EAAE,KAAK,SAAS,OAAO,OAAO,CAAC;AAAA,IACpE,SACO,KAAK;AACR,wBAAkB,QAAQ,OAAO,GAAG;AAAA,IACxC;AAEA,UAAM,eAAe,SAAS,MAAM;AACpC,QAAI,2BAA2B,YAC1B,KAAK,iBAAiB,eAAe;AACtC,wBAAkB,gBAAgB,MAAM,OAAO,QAAQ;AAEnD,YAAI,cAAc;AACd,cAAI,MAAuC;AAGvC,mBAAO,eAAe,qCACd,eAAe,GAAG,CAAC,0CAA0C;AACrE,mBAAO,MAAM,oBAAoB,KAAK;AACtC,mBAAO,MAAM,GAAG;AAChB,mBAAO,SAAS;AAAA,UACpB;AACA,cAAI;AACA,mBAAO,MAAM,aAAa,OAAO,EAAE,KAAK,SAAS,OAAO,OAAO,CAAC;AAAA,UACpE,SACO,UAAU;AACb,gBAAI,oBAAoB,OAAO;AAC3B,oBAAM;AAAA,YACV;AAAA,UACJ;AAAA,QACJ;AACA,YAAI,KAAK,eAAe;AACpB,cAAI,MAAuC;AAGvC,mBAAO,eAAe,qCACd,eAAe,GAAG,CAAC,yCAAyC;AACpE,mBAAO,MAAM,oBAAoB,KAAK;AACtC,mBAAO,MAAM,GAAG;AAChB,mBAAO,SAAS;AAAA,UACpB;AACA,iBAAO,KAAK,cAAc,OAAO,EAAE,KAAK,SAAS,MAAM,CAAC;AAAA,QAC5D;AACA,cAAM;AAAA,MACV,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,kBAAkB,EAAE,KAAK,YAAY,SAAS,MAAO,GAAG;AACpD,UAAM,SAAS,KAAK,QAAQ,IAAI,QAAQ,MAAM,KAAK,CAAC;AACpD,eAAW,SAAS,QAAQ;AACxB,UAAI;AAGJ,YAAM,cAAc,MAAM,MAAM,EAAE,KAAK,YAAY,SAAS,MAAM,CAAC;AACnE,UAAI,aAAa;AACb,YAAI,MAAuC;AAGvC,cAAI,uBAAuB,SAAS;AAChC,mBAAO,KAAK,iBAAiB,eAAe,GAAG,CAAC,+HAEoB,KAAK;AAAA,UAC7E;AAAA,QACJ;AAGA,iBAAS;AACT,YAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,WAAW,GAAG;AAE9C,mBAAS;AAAA,QACb,WACS,YAAY,gBAAgB;AAAA,QACjC,OAAO,KAAK,WAAW,EAAE,WAAW,GAAG;AAEvC,mBAAS;AAAA,QACb,WACS,OAAO,gBAAgB,WAAW;AAIvC,mBAAS;AAAA,QACb;AAEA,eAAO,EAAE,OAAO,OAAO;AAAA,MAC3B;AAAA,IACJ;AAEA,WAAO,CAAC;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,kBAAkB,SAAS,SAAS,eAAe;AAC/C,SAAK,mBAAmB,IAAI,QAAQ,iBAAiB,OAAO,CAAC;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,SAAS;AACrB,SAAK,gBAAgB,iBAAiB,OAAO;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,OAAO;AACjB,QAAI,MAAuC;AACvC,yBAAO,OAAO,OAAO,UAAU;AAAA,QAC3B,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AACD,yBAAO,UAAU,OAAO,SAAS;AAAA,QAC7B,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AACD,yBAAO,OAAO,MAAM,SAAS,UAAU;AAAA,QACnC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AACD,yBAAO,UAAU,MAAM,SAAS,UAAU;AAAA,QACtC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AACD,yBAAO,OAAO,MAAM,QAAQ,UAAU;AAAA,QAClC,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACf,CAAC;AAAA,IACL;AACA,QAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,MAAM,GAAG;AACjC,WAAK,QAAQ,IAAI,MAAM,QAAQ,CAAC,CAAC;AAAA,IACrC;AAGA,SAAK,QAAQ,IAAI,MAAM,MAAM,EAAE,KAAK,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,OAAO;AACnB,QAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,MAAM,GAAG;AACjC,YAAM,IAAI,aAAa,8CAA8C;AAAA,QACjE,QAAQ,MAAM;AAAA,MAClB,CAAC;AAAA,IACL;AACA,UAAM,aAAa,KAAK,QAAQ,IAAI,MAAM,MAAM,EAAE,QAAQ,KAAK;AAC/D,QAAI,aAAa,IAAI;AACjB,WAAK,QAAQ,IAAI,MAAM,MAAM,EAAE,OAAO,YAAY,CAAC;AAAA,IACvD,OACK;AACD,YAAM,IAAI,aAAa,uCAAuC;AAAA,IAClE;AAAA,EACJ;AACJ;;;AC9XA,IAAI;AAQG,IAAM,2BAA2B,MAAM;AAC1C,MAAI,CAAC,eAAe;AAChB,oBAAgB,IAAI,OAAO;AAE3B,kBAAc,iBAAiB;AAC/B,kBAAc,iBAAiB;AAAA,EACnC;AACA,SAAO;AACX;;;ACMA,SAAS,cAAc,SAAS,SAAS,QAAQ;AAC7C,MAAI;AACJ,MAAI,OAAO,YAAY,UAAU;AAC7B,UAAM,aAAa,IAAI,IAAI,SAAS,SAAS,IAAI;AACjD,QAAI,MAAuC;AACvC,UAAI,EAAE,QAAQ,WAAW,GAAG,KAAK,QAAQ,WAAW,MAAM,IAAI;AAC1D,cAAM,IAAI,aAAa,kBAAkB;AAAA,UACrC,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,WAAW;AAAA,QACf,CAAC;AAAA,MACL;AAGA,YAAM,eAAe,QAAQ,WAAW,MAAM,IACxC,WAAW,WACX;AAEN,YAAM,YAAY;AAClB,UAAI,IAAI,OAAO,GAAG,SAAS,EAAE,EAAE,KAAK,YAAY,GAAG;AAC/C,eAAO,MAAM,0EACK,SAAS,uGACuC;AAAA,MACtE;AAAA,IACJ;AACA,UAAM,gBAAgB,CAAC,EAAE,IAAI,MAAM;AAC/B,UAAI,MAAuC;AACvC,YAAI,IAAI,aAAa,WAAW,YAC5B,IAAI,WAAW,WAAW,QAAQ;AAClC,iBAAO,MAAM,GAAG,OAAO,gDAChB,IAAI,SAAS,CAAC,mFACc;AAAA,QACvC;AAAA,MACJ;AACA,aAAO,IAAI,SAAS,WAAW;AAAA,IACnC;AAEA,YAAQ,IAAI,MAAM,eAAe,SAAS,MAAM;AAAA,EACpD,WACS,mBAAmB,QAAQ;AAEhC,YAAQ,IAAI,YAAY,SAAS,SAAS,MAAM;AAAA,EACpD,WACS,OAAO,YAAY,YAAY;AAEpC,YAAQ,IAAI,MAAM,SAAS,SAAS,MAAM;AAAA,EAC9C,WACS,mBAAmB,OAAO;AAC/B,YAAQ;AAAA,EACZ,OACK;AACD,UAAM,IAAI,aAAa,0BAA0B;AAAA,MAC7C,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,WAAW;AAAA,IACf,CAAC;AAAA,EACL;AACA,QAAMA,iBAAgB,yBAAyB;AAC/C,EAAAA,eAAc,cAAc,KAAK;AACjC,SAAO;AACX;", "names": ["defaultRouter"]}