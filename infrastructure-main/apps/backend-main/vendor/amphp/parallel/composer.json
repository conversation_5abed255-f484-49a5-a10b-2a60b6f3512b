{"name": "amphp/parallel", "description": "Parallel processing component for Amp.", "keywords": ["asynchronous", "async", "concurrent", "multi-threading", "multi-processing"], "homepage": "https://github.com/amphp/parallel", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "amphp/amp": "^3", "amphp/byte-stream": "^2", "amphp/cache": "^2", "amphp/parser": "^1", "amphp/pipeline": "^1", "amphp/process": "^2", "amphp/serialization": "^1", "amphp/socket": "^2", "amphp/sync": "^2", "revolt/event-loop": "^1"}, "require-dev": {"phpunit/phpunit": "^9", "amphp/phpunit-util": "^3", "amphp/php-cs-fixer-config": "^2", "psalm/phar": "^5.18"}, "autoload": {"psr-4": {"Amp\\Parallel\\": "src"}, "files": ["src/Context/functions.php", "src/Context/Internal/functions.php", "src/Ipc/functions.php", "src/Worker/functions.php"]}, "autoload-dev": {"psr-4": {"App\\Worker\\": "examples/worker", "Amp\\Parallel\\Test\\": "test"}}, "scripts": {"check": ["@cs", "@test"], "cs": "php-cs-fixer fix -v --diff --dry-run", "cs-fix": "php-cs-fixer fix -v --diff", "test": "@php -dzend.assertions=1 -dassert.exception=1 ./vendor/bin/phpunit --coverage-text"}}