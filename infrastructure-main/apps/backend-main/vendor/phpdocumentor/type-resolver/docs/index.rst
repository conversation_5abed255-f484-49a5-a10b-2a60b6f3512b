=============
Type resolver
=============

This project part of the phpDocumentor project. It is capable of creating an object structure of the type
specifications found in the PHPDoc blocks of a project. This can be useful for static analysis of a project
or other behavior that requires knowledge of the types used in a project like automatically build forms.

This project aims to cover all types that are available in PHPDoc and PHP itself. And is open for extension by
third party developers.

.. toctree::
   :maxdepth: 2
   :hidden:

   index
   getting-started
