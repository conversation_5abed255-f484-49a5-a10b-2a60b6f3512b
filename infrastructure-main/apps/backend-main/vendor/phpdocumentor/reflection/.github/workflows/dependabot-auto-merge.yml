name: "Dependabot auto-merge"

on: # yamllint disable-line rule:truthy
  pull_request_target: null

permissions:
  contents: "write"

jobs:
  dependabot:
    runs-on: "ubuntu-latest"
    if: "${{ github.actor == 'dependabot[bot]' }}"
    steps:
      - name: "Dependabot metadata"
        id: "metadata"
        uses: "dependabot/fetch-metadata@v2.2.0"
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
      - name: "Enable auto-merge for Dependabot PRs"
        if: "${{ steps.dependabot-metadata.outputs.update-type != 'version-update:semver-major' }}"
        run: "gh pr merge --auto --merge \"$PR_URL\""
        env:
          PR_URL: "${{github.event.pull_request.html_url}}"
          GITHUB_TOKEN: "${{secrets.GITHUB_TOKEN}}"
