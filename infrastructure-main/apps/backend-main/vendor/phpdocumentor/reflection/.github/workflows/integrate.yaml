# https://docs.github.com/en/actions

name: "Integrate"

on: # yamllint disable-line rule:truthy
  push:
    branches:
      - "6.x"
  pull_request: null
  # Allow manually triggering the workflow.
  workflow_dispatch: null

jobs:
  code-coverage:
    name: "Code Coverage"
    uses: "phpDocumentor/.github/.github/workflows/code-coverage.yml@v0.8"
    with:
      php-version: "8.2"

  coding-standards:
    name: "Coding Standards"
    runs-on: "ubuntu-22.04"
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v4"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "8.2"
          tools: "cs2pr"

      - name: "Install dependencies with Composer"
        uses: "ramsey/composer-install@v3"
        with:
          dependency-versions: "locked"

      - name: "Run PHP_CodeSniffer"
        run: "vendor/bin/phpcs -q --no-colors --report=checkstyle | cs2pr"

  dependency-analysis:
    name: "Dependency analysis"
    uses: "phpDocumentor/.github/.github/workflows/dependency-analysis.yml@v0.8"
    with:
      php-version: "8.2"

  lint-root:
    name: "Lint root"
    uses: "phpDocumentor/.github/.github/workflows/lint.yml@v0.8"
    with:
      php-version: "8.2"
      composer-options: "--no-check-publish --ansi"

  static-analysis:
    name: "Static analysis"
    uses: "phpDocumentor/.github/.github/workflows/static-analysis.yml@main"
    with:
      php-version: "8.2"
      php-extensions: "none, ctype, dom, json, mbstring, phar, simplexml, tokenizer, xml, xmlwriter, fileinfo, pcntl, posix"

  unit-tests:
    name: "Unit test"
    uses: "phpDocumentor/.github/.github/workflows/continuous-integration.yml@v0.8"

  integration-tests:
    name: "Integration test"
    uses: "phpDocumentor/.github/.github/workflows/continuous-integration.yml@v0.8"
    needs: "unit-tests"
    with:
      test-suite: "integration"
