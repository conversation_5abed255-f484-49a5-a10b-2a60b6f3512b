<?php

declare(strict_types=1);

/**
 * This file is part of phpDocumentor.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 *
 * @link http://phpdoc.org
 */

namespace phpDocumentor\Reflection\Metadata;

interface MetaDataContainer
{
    public function addMetadata(Metadata $metadata): void;

    /** @return Metadata[] */
    public function getMetadata(): array;
}
