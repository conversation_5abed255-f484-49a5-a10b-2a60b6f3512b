<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'voku\\' => array($vendorDir . '/voku/portable-ascii/src/voku'),
    'phpDocumentor\\Reflection\\' => array($vendorDir . '/phpdocumentor/reflection-common/src', $vendorDir . '/phpdocumentor/reflection-docblock/src', $vendorDir . '/phpdocumentor/type-resolver/src'),
    'phpDocumentor\\' => array($vendorDir . '/phpdocumentor/reflection/src/phpDocumentor'),
    'Whoops\\' => array($vendorDir . '/filp/whoops/src/Whoops'),
    'Webmozart\\Assert\\' => array($vendorDir . '/webmozart/assert/src'),
    'TijsVerkoyen\\CssToInlineStyles\\' => array($vendorDir . '/tijsverkoyen/css-to-inline-styles/src'),
    'Tests\\' => array($baseDir . '/tests'),
    'Termwind\\' => array($vendorDir . '/nunomaduro/termwind/src'),
    'Symfony\\Polyfill\\Uuid\\' => array($vendorDir . '/symfony/polyfill-uuid'),
    'Symfony\\Polyfill\\Php83\\' => array($vendorDir . '/symfony/polyfill-php83'),
    'Symfony\\Polyfill\\Php80\\' => array($vendorDir . '/symfony/polyfill-php80'),
    'Symfony\\Polyfill\\Mbstring\\' => array($vendorDir . '/symfony/polyfill-mbstring'),
    'Symfony\\Polyfill\\Intl\\Normalizer\\' => array($vendorDir . '/symfony/polyfill-intl-normalizer'),
    'Symfony\\Polyfill\\Intl\\Idn\\' => array($vendorDir . '/symfony/polyfill-intl-idn'),
    'Symfony\\Polyfill\\Intl\\Grapheme\\' => array($vendorDir . '/symfony/polyfill-intl-grapheme'),
    'Symfony\\Polyfill\\Ctype\\' => array($vendorDir . '/symfony/polyfill-ctype'),
    'Symfony\\Contracts\\Translation\\' => array($vendorDir . '/symfony/translation-contracts'),
    'Symfony\\Contracts\\Service\\' => array($vendorDir . '/symfony/service-contracts'),
    'Symfony\\Contracts\\HttpClient\\' => array($vendorDir . '/symfony/http-client-contracts'),
    'Symfony\\Contracts\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher-contracts'),
    'Symfony\\Component\\Yaml\\' => array($vendorDir . '/symfony/yaml'),
    'Symfony\\Component\\VarDumper\\' => array($vendorDir . '/symfony/var-dumper'),
    'Symfony\\Component\\Uid\\' => array($vendorDir . '/symfony/uid'),
    'Symfony\\Component\\Translation\\' => array($vendorDir . '/symfony/translation'),
    'Symfony\\Component\\String\\' => array($vendorDir . '/symfony/string'),
    'Symfony\\Component\\Routing\\' => array($vendorDir . '/symfony/routing'),
    'Symfony\\Component\\Process\\' => array($vendorDir . '/symfony/process'),
    'Symfony\\Component\\OptionsResolver\\' => array($vendorDir . '/symfony/options-resolver'),
    'Symfony\\Component\\Mime\\' => array($vendorDir . '/symfony/mime'),
    'Symfony\\Component\\Mailer\\' => array($vendorDir . '/symfony/mailer'),
    'Symfony\\Component\\HttpKernel\\' => array($vendorDir . '/symfony/http-kernel'),
    'Symfony\\Component\\HttpFoundation\\' => array($vendorDir . '/symfony/http-foundation'),
    'Symfony\\Component\\HttpClient\\' => array($vendorDir . '/symfony/http-client'),
    'Symfony\\Component\\HtmlSanitizer\\' => array($vendorDir . '/symfony/html-sanitizer'),
    'Symfony\\Component\\Finder\\' => array($vendorDir . '/symfony/finder'),
    'Symfony\\Component\\EventDispatcher\\' => array($vendorDir . '/symfony/event-dispatcher'),
    'Symfony\\Component\\ErrorHandler\\' => array($vendorDir . '/symfony/error-handler'),
    'Symfony\\Component\\CssSelector\\' => array($vendorDir . '/symfony/css-selector'),
    'Symfony\\Component\\Console\\' => array($vendorDir . '/symfony/console'),
    'Symfony\\Bridge\\PsrHttpMessage\\' => array($vendorDir . '/symfony/psr-http-message-bridge'),
    'SpomkyLabs\\Pki\\' => array($vendorDir . '/spomky-labs/pki-framework/src'),
    'Spatie\\StructureDiscoverer\\' => array($vendorDir . '/spatie/php-structure-discoverer/src'),
    'Spatie\\LaravelPackageTools\\' => array($vendorDir . '/spatie/laravel-package-tools/src'),
    'Spatie\\LaravelImageOptimizer\\' => array($vendorDir . '/spatie/laravel-image-optimizer/src'),
    'Spatie\\LaravelIgnition\\' => array($vendorDir . '/spatie/error-solutions/legacy/laravel-ignition', $vendorDir . '/spatie/laravel-ignition/src'),
    'Spatie\\LaravelData\\' => array($vendorDir . '/spatie/laravel-data/src'),
    'Spatie\\Invade\\' => array($vendorDir . '/spatie/invade/src'),
    'Spatie\\ImageOptimizer\\' => array($vendorDir . '/spatie/image-optimizer/src'),
    'Spatie\\Ignition\\' => array($vendorDir . '/spatie/error-solutions/legacy/ignition', $vendorDir . '/spatie/ignition/src'),
    'Spatie\\FlareClient\\' => array($vendorDir . '/spatie/flare-client-php/src'),
    'Spatie\\ErrorSolutions\\' => array($vendorDir . '/spatie/error-solutions/src'),
    'Spatie\\Color\\' => array($vendorDir . '/spatie/color/src'),
    'Spatie\\Backtrace\\' => array($vendorDir . '/spatie/backtrace/src'),
    'Sentry\\' => array($vendorDir . '/sentry/sentry/src'),
    'RyanChandler\\BladeCaptureDirective\\Database\\Factories\\' => array($vendorDir . '/ryangjchandler/blade-capture-directive/database/factories'),
    'RyanChandler\\BladeCaptureDirective\\' => array($vendorDir . '/ryangjchandler/blade-capture-directive/src'),
    'Revolt\\' => array($vendorDir . '/revolt/event-loop/src'),
    'Ramsey\\Uuid\\' => array($vendorDir . '/ramsey/uuid/src'),
    'Ramsey\\Collection\\' => array($vendorDir . '/ramsey/collection/src'),
    'Pusher\\' => array($vendorDir . '/pusher/pusher-php-server/src'),
    'Psy\\' => array($vendorDir . '/psy/psysh/src'),
    'Psr\\SimpleCache\\' => array($vendorDir . '/psr/simple-cache/src'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/src'),
    'Psr\\Http\\Message\\' => array($vendorDir . '/psr/http-factory/src', $vendorDir . '/psr/http-message/src'),
    'Psr\\Http\\Client\\' => array($vendorDir . '/psr/http-client/src'),
    'Psr\\EventDispatcher\\' => array($vendorDir . '/psr/event-dispatcher/src'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Psr\\Clock\\' => array($vendorDir . '/psr/clock/src'),
    'Psr\\Cache\\' => array($vendorDir . '/psr/cache/src'),
    'Predis\\' => array($vendorDir . '/predis/predis/src'),
    'PostHog\\' => array($vendorDir . '/posthog/posthog-php/lib'),
    'PhpParser\\' => array($vendorDir . '/nikic/php-parser/lib/PhpParser'),
    'PhpOption\\' => array($vendorDir . '/phpoption/phpoption/src/PhpOption'),
    'ParagonIE\\ConstantTime\\' => array($vendorDir . '/paragonie/constant_time_encoding/src'),
    'PHPStan\\PhpDocParser\\' => array($vendorDir . '/phpstan/phpdoc-parser/src'),
    'OpenSpout\\' => array($vendorDir . '/openspout/openspout/src'),
    'Nyholm\\Psr7\\' => array($vendorDir . '/nyholm/psr7/src'),
    'NunoMaduro\\Collision\\' => array($vendorDir . '/nunomaduro/collision/src'),
    'NotificationChannels\\WebPush\\' => array($vendorDir . '/laravel-notification-channels/webpush/src'),
    'NotificationChannels\\OneSignal\\' => array($vendorDir . '/laravel-notification-channels/onesignal/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'Mockery\\' => array($vendorDir . '/mockery/mockery/library/Mockery'),
    'Minishlink\\WebPush\\' => array($vendorDir . '/minishlink/web-push/src'),
    'Masterminds\\' => array($vendorDir . '/masterminds/html5/src'),
    'Livewire\\' => array($vendorDir . '/livewire/livewire/src'),
    'LibDNS\\' => array($vendorDir . '/daverandom/libdns/src'),
    'League\\Uri\\' => array($vendorDir . '/league/uri', $vendorDir . '/league/uri-interfaces'),
    'League\\MimeTypeDetection\\' => array($vendorDir . '/league/mime-type-detection/src'),
    'League\\Flysystem\\Local\\' => array($vendorDir . '/league/flysystem-local'),
    'League\\Flysystem\\AwsS3V3\\' => array($vendorDir . '/league/flysystem-aws-s3-v3'),
    'League\\Flysystem\\' => array($vendorDir . '/league/flysystem/src'),
    'League\\Csv\\' => array($vendorDir . '/league/csv/src'),
    'League\\Config\\' => array($vendorDir . '/league/config/src'),
    'League\\CommonMark\\' => array($vendorDir . '/league/commonmark/src'),
    'Laravel\\Tinker\\' => array($vendorDir . '/laravel/tinker/src'),
    'Laravel\\Telescope\\Database\\Factories\\' => array($vendorDir . '/laravel/telescope/database/factories'),
    'Laravel\\Telescope\\' => array($vendorDir . '/laravel/telescope/src'),
    'Laravel\\SerializableClosure\\' => array($vendorDir . '/laravel/serializable-closure/src'),
    'Laravel\\Sanctum\\' => array($vendorDir . '/laravel/sanctum/src'),
    'Laravel\\Sail\\' => array($vendorDir . '/laravel/sail/src'),
    'Laravel\\Prompts\\' => array($vendorDir . '/laravel/prompts/src'),
    'Kirschbaum\\PowerJoins\\' => array($vendorDir . '/kirschbaum-development/eloquent-power-joins/src'),
    'Kelunik\\Certificate\\' => array($vendorDir . '/kelunik/certificate/src'),
    'Jose\\Component\\' => array($vendorDir . '/web-token/jwt-library'),
    'JmesPath\\' => array($vendorDir . '/mtdowling/jmespath.php/src'),
    'Jean85\\' => array($vendorDir . '/jean85/pretty-package-versions/src'),
    'Illuminate\\Support\\' => array($vendorDir . '/laravel/framework/src/Illuminate/Macroable', $vendorDir . '/laravel/framework/src/Illuminate/Collections', $vendorDir . '/laravel/framework/src/Illuminate/Conditionable'),
    'Illuminate\\' => array($vendorDir . '/laravel/framework/src/Illuminate'),
    'GuzzleHttp\\UriTemplate\\' => array($vendorDir . '/guzzlehttp/uri-template/src'),
    'GuzzleHttp\\Psr7\\' => array($vendorDir . '/guzzlehttp/psr7/src'),
    'GuzzleHttp\\Promise\\' => array($vendorDir . '/guzzlehttp/promises/src'),
    'GuzzleHttp\\' => array($vendorDir . '/guzzlehttp/guzzle/src'),
    'GrahamCampbell\\ResultType\\' => array($vendorDir . '/graham-campbell/result-type/src'),
    'Fruitcake\\Cors\\' => array($vendorDir . '/fruitcake/php-cors/src'),
    'Filament\\Widgets\\' => array($vendorDir . '/filament/widgets/src'),
    'Filament\\Tables\\' => array($vendorDir . '/filament/tables/src'),
    'Filament\\Support\\' => array($vendorDir . '/filament/support/src'),
    'Filament\\Notifications\\' => array($vendorDir . '/filament/notifications/src'),
    'Filament\\Infolists\\' => array($vendorDir . '/filament/infolists/src'),
    'Filament\\Forms\\' => array($vendorDir . '/filament/forms/src'),
    'Filament\\Actions\\' => array($vendorDir . '/filament/actions/src'),
    'Filament\\' => array($vendorDir . '/filament/filament/src'),
    'Faker\\' => array($vendorDir . '/fakerphp/faker/src/Faker'),
    'Egulias\\EmailValidator\\' => array($vendorDir . '/egulias/email-validator/src'),
    'Dotenv\\' => array($vendorDir . '/vlucas/phpdotenv/src'),
    'Doctrine\\Inflector\\' => array($vendorDir . '/doctrine/inflector/lib/Doctrine/Inflector'),
    'Doctrine\\Deprecations\\' => array($vendorDir . '/doctrine/deprecations/lib/Doctrine/Deprecations'),
    'Doctrine\\DBAL\\' => array($vendorDir . '/doctrine/dbal/src'),
    'Doctrine\\Common\\Lexer\\' => array($vendorDir . '/doctrine/lexer/src'),
    'Doctrine\\Common\\Cache\\' => array($vendorDir . '/doctrine/cache/lib/Doctrine/Common/Cache'),
    'Doctrine\\Common\\' => array($vendorDir . '/doctrine/event-manager/src'),
    'Dflydev\\DotAccessData\\' => array($vendorDir . '/dflydev/dot-access-data/src'),
    'DeepCopy\\' => array($vendorDir . '/myclabs/deep-copy/src/DeepCopy'),
    'Database\\Seeders\\' => array($baseDir . '/database/seeders', $vendorDir . '/laravel/pint/database/seeders'),
    'Database\\Factories\\' => array($baseDir . '/database/factories', $vendorDir . '/laravel/pint/database/factories'),
    'DanHarrin\\LivewireRateLimiting\\' => array($vendorDir . '/danharrin/livewire-rate-limiting/src'),
    'DanHarrin\\DateFormatConverter\\' => array($vendorDir . '/danharrin/date-format-converter/src'),
    'Cron\\' => array($vendorDir . '/dragonmantank/cron-expression/src/Cron'),
    'Carbon\\Doctrine\\' => array($vendorDir . '/carbonphp/carbon-doctrine-types/src/Carbon/Doctrine'),
    'Carbon\\' => array($vendorDir . '/nesbot/carbon/src/Carbon'),
    'Brick\\Math\\' => array($vendorDir . '/brick/math/src'),
    'BladeUI\\Icons\\' => array($vendorDir . '/blade-ui-kit/blade-icons/src'),
    'BladeUI\\Heroicons\\' => array($vendorDir . '/blade-ui-kit/blade-heroicons/src'),
    'Berkayk\\OneSignal\\' => array($vendorDir . '/berkayk/onesignal-laravel/src'),
    'Bepsvpt\\Blurhash\\' => array($vendorDir . '/bepsvpt/blurhash/src'),
    'Base64Url\\' => array($vendorDir . '/spomky-labs/base64url/src'),
    'Aws\\' => array($vendorDir . '/aws/aws-sdk-php/src'),
    'App\\' => array($baseDir . '/app', $vendorDir . '/laravel/pint/app'),
    'AnourValar\\EloquentSerialize\\' => array($vendorDir . '/anourvalar/eloquent-serialize/src'),
    'Amp\\Sync\\' => array($vendorDir . '/amphp/sync/src'),
    'Amp\\Socket\\' => array($vendorDir . '/amphp/socket/src'),
    'Amp\\Serialization\\' => array($vendorDir . '/amphp/serialization/src'),
    'Amp\\Process\\' => array($vendorDir . '/amphp/process/src'),
    'Amp\\Pipeline\\' => array($vendorDir . '/amphp/pipeline/src'),
    'Amp\\Parser\\' => array($vendorDir . '/amphp/parser/src'),
    'Amp\\Parallel\\' => array($vendorDir . '/amphp/parallel/src'),
    'Amp\\Dns\\' => array($vendorDir . '/amphp/dns/src'),
    'Amp\\Cache\\' => array($vendorDir . '/amphp/cache/src'),
    'Amp\\ByteStream\\' => array($vendorDir . '/amphp/byte-stream/src'),
    'Amp\\' => array($vendorDir . '/amphp/amp/src'),
    'AfricasTalking\\SDK\\' => array($vendorDir . '/africastalking/africastalking/src'),
);
