{"name": "spatie/php-structure-discoverer", "description": "Automatically discover structures within your PHP application", "keywords": ["laravel", "php", "discover", "php-structure-discoverer"], "homepage": "https://github.com/spatie/php-structure-discoverer", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.1", "amphp/amp": "^v3.0", "amphp/parallel": "^2.2", "illuminate/collections": "^10.0|^11.0|^12.0", "spatie/laravel-package-tools": "^1.4.3", "symfony/finder": "^6.0|^7.0"}, "require-dev": {"illuminate/console": "^10.0|^11.0|^12.0", "laravel/pint": "^1.0", "nunomaduro/collision": "^7.0|^8.0", "orchestra/testbench": "^7.0|^8.0|^9.0|^10.0", "pestphp/pest": "^2.0|^3.0", "pestphp/pest-plugin-laravel": "^2.0|^3.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5|^10.0|^11.5.3", "spatie/laravel-ray": "^1.26"}, "autoload": {"psr-4": {"Spatie\\StructureDiscoverer\\": "src"}}, "autoload-dev": {"psr-4": {"Spatie\\StructureDiscoverer\\Tests\\": "tests"}}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "test": "vendor/bin/pest", "test-coverage": "vendor/bin/pest --coverage", "format": "vendor/bin/pint"}, "config": {"sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "phpstan/extension-installer": true}}, "extra": {"laravel": {"providers": ["Spatie\\StructureDiscoverer\\StructureDiscovererServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true}