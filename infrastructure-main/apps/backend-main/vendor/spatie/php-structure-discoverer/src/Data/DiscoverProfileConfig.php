<?php

namespace Spa<PERSON>\StructureDiscoverer\Data;

use Spa<PERSON>\StructureDiscoverer\Cache\DiscoverCacheDriver;
use Spa<PERSON>\StructureDiscoverer\DiscoverConditions\ExactDiscoverCondition;
use Spa<PERSON>\StructureDiscoverer\DiscoverWorkers\DiscoverWorker;
use Spa<PERSON>\StructureDiscoverer\Enums\Sort;
use Spatie\StructureDiscoverer\StructureParsers\StructureParser;

class DiscoverProfileConfig
{
    /**
     * @param array<string> $directories
     * @param array<string> $ignoredFiles
     */
    public function __construct(
        public array $directories,
        public array $ignoredFiles,
        public bool $full,
        public DiscoverWorker $worker,
        public ?DiscoverCacheDriver $cacheDriver,
        public ?string $cacheId,
        public bool $withChains,
        public ExactDiscoverCondition $conditions,
        public ?Sort $sort,
        public bool $reverseSorting,
        public StructureParser $structureParser,
        public ?string $reflectionBasePath,
        public ?string $reflectionRootNamespace,
    ) {
    }

    public function shouldUseCache(): bool
    {
        return $this->cacheId !== null && $this->cacheDriver !== null;
    }
}
