<?php

namespace Spa<PERSON>\StructureDiscoverer\DiscoverConditions;

use Spa<PERSON>\StructureDiscoverer\Data\DiscoveredAttribute;
use <PERSON><PERSON>\StructureDiscoverer\Data\DiscoveredClass;
use Spa<PERSON>\StructureDiscoverer\Data\DiscoveredEnum;
use Spa<PERSON>\StructureDiscoverer\Data\DiscoveredInterface;
use <PERSON><PERSON>\StructureDiscoverer\Data\DiscoveredStructure;

class AttributeDiscoverCondition extends DiscoverCondition
{
    /** @var string[] */
    private array $classes;

    public function __construct(
        string ...$classes,
    ) {
        $this->classes = $classes;
    }

    public function satisfies(DiscoveredStructure $discoveredData): bool
    {
        $hasAttributes = $discoveredData instanceof DiscoveredInterface
            || $discoveredData instanceof DiscoveredEnum
            || $discoveredData instanceof DiscoveredClass;

        if (! $hasAttributes) {
            return false;
        }

        $foundAttributes = array_filter(
            $discoveredData->attributes,
            fn (DiscoveredAttribute $attribute) => in_array($attribute->class, $this->classes)
        );

        return count($foundAttributes) > 0;
    }
}
