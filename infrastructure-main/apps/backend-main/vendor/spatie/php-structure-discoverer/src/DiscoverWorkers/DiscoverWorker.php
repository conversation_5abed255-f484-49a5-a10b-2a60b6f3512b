<?php

namespace Spa<PERSON>\StructureDiscoverer\DiscoverWorkers;

use Illuminate\Support\Collection;
use Spa<PERSON>\StructureDiscoverer\Data\DiscoveredStructure;
use Spatie\StructureDiscoverer\Data\DiscoverProfileConfig;

interface DiscoverWorker
{
    /**
     * @param Collection<int, string> $filenames
     * @param DiscoverProfileConfig $config
     *
     * @return array<DiscoveredStructure>
     */
    public function run(Collection $filenames, DiscoverProfileConfig $config): array;
}
