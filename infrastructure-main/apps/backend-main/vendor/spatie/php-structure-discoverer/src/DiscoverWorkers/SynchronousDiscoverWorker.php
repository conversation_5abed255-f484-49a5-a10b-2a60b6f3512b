<?php

namespace Spa<PERSON>\StructureDiscoverer\DiscoverWorkers;

use Illuminate\Support\Collection;
use <PERSON><PERSON>\StructureDiscoverer\Data\DiscoveredStructure;
use <PERSON><PERSON>\StructureDiscoverer\Data\DiscoverProfileConfig;

class SynchronousDiscoverWorker implements DiscoverWorker
{
    public function __construct()
    {
    }

    /**
     * @param Collection<int, string> $filenames
     * @param DiscoverProfileConfig $config
     *
     * @return array<DiscoveredStructure>
     */
    public function run(Collection $filenames, DiscoverProfileConfig $config): array
    {
        return $config->structureParser->execute($filenames->toArray());
    }
}
