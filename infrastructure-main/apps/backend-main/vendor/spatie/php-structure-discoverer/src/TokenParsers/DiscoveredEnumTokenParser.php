<?php

namespace Spa<PERSON>\StructureDiscoverer\TokenParsers;

use <PERSON><PERSON>\StructureDiscoverer\Collections\TokenCollection;
use <PERSON><PERSON>\StructureDiscoverer\Collections\UsageCollection;
use Spa<PERSON>\StructureDiscoverer\Data\DiscoveredAttribute;
use <PERSON><PERSON>\StructureDiscoverer\Data\DiscoveredEnum;
use <PERSON><PERSON>\StructureDiscoverer\Enums\DiscoveredEnumType;

class DiscoveredEnumTokenParser
{
    public function __construct(
        protected StructureHeadTokenParser $structureHeadResolver = new StructureHeadTokenParser(),
    ) {
    }

    /**
     * @param DiscoveredAttribute[] $attributes
     */
    public function execute(
        int $index,
        TokenCollection $tokens,
        string $namespace,
        UsageCollection $usages,
        array $attributes,
        string $file,
    ): DiscoveredEnum {
        $head = $this->structureHeadResolver->execute($index, $tokens, $namespace, $usages);

        return new DiscoveredEnum(
            $tokens->get($index)->text,
            $file,
            $namespace,
            $this->resolveType($index, $tokens),
            $head->implements,
            $attributes,
        );
    }

    protected function resolveType(
        int $index,
        TokenCollection $tokens,
    ): DiscoveredEnumType {
        $typeToken = $tokens->get($index + 2);

        if ($typeToken === null
            || ! $typeToken->is(T_STRING)
            || ! in_array($typeToken->text, ['int', 'string'])
        ) {
            return DiscoveredEnumType::Unit;
        }

        if ($typeToken->text === 'int') {
            return DiscoveredEnumType::Int;
        }

        if ($typeToken->text === 'string') {
            return DiscoveredEnumType::String;
        }
    }
}
