<?php

namespace Spa<PERSON>\StructureDiscoverer;

use <PERSON><PERSON>\LaravelPackageTools\Package;
use <PERSON><PERSON>\LaravelPackageTools\PackageServiceProvider;
use Spa<PERSON>\StructureDiscoverer\Commands\CacheStructureScoutsCommand;
use <PERSON><PERSON>\StructureDiscoverer\Commands\ClearStructureScoutsCommand;
use Spatie\StructureDiscoverer\Support\DiscoverCacheDriverFactory;

class StructureDiscovererServiceProvider extends PackageServiceProvider
{
    public function configurePackage(Package $package): void
    {
        $package
            ->name('structure-discoverer')
            ->hasConfigFile()
            ->hasCommand(CacheStructureScoutsCommand::class)
            ->hasCommand(ClearStructureScoutsCommand::class);
    }

    public function packageRegistered(): void
    {
        $this->app->bind(Discover::class, fn ($app, $provided) => new Discover(
            directories: $provided['directories'] ?? [],
            ignoredFiles: config('structure-discoverer.ignored_files'),
            cacheDriver: DiscoverCacheDriverFactory::create(config('structure-discoverer.cache')),
        ));
    }
}
