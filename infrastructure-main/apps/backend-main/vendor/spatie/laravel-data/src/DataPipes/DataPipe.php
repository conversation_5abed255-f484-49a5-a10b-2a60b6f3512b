<?php

namespace Spa<PERSON>\LaravelData\DataPipes;

use <PERSON><PERSON>\LaravelData\Support\Creation\CreationContext;
use <PERSON>tie\LaravelData\Support\DataClass;

interface DataPipe
{
    /**
     * @param array<array-key, mixed> $properties
     *
     * @return array<array-key, mixed>
     */
    public function handle(mixed $payload, DataClass $class, array $properties, CreationContext $creationContext): array;
}
