<?php

namespace Spatie\LaravelData\Resolvers\Concerns;

use <PERSON><PERSON>\LaravelData\Exceptions\MaxTransformationDepthReached;
use <PERSON><PERSON>\LaravelData\Support\Transformation\TransformationContext;

trait ChecksTransformationDepth
{
    public function hasReachedMaxTransformationDepth(TransformationContext $context): bool
    {
        return $context->maxDepth !== null && $context->depth >= $context->maxDepth;
    }

    public function handleMaxDepthReached(TransformationContext $context): array
    {
        if ($context->throwWhenMaxDepthReached) {
            throw MaxTransformationDepthReached::create($context->maxDepth);
        }

        return [];
    }
}
