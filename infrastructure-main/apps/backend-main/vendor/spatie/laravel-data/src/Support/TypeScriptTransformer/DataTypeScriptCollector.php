<?php

namespace Spatie\LaravelData\Support\TypeScriptTransformer;

use ReflectionClass;
use <PERSON><PERSON>\LaravelData\Contracts\BaseData;
use <PERSON><PERSON>\TypeScriptTransformer\Collectors\Collector;
use <PERSON><PERSON>\TypeScriptTransformer\Structures\TransformedType;

class DataTypeScriptCollector extends Collector
{
    public function getTransformedType(ReflectionClass $class): ?TransformedType
    {
        if (! $class->isSubclassOf(BaseData::class)) {
            return null;
        }

        $transformer = new DataTypeScriptTransformer($this->config);

        return $transformer->transform($class, $class->getShortName());
    }
}
