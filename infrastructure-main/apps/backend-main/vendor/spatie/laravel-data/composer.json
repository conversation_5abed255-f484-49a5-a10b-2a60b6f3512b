{"name": "spatie/laravel-data", "description": "Create unified resources and data transfer objects", "keywords": ["spatie", "laravel", "laravel-data"], "homepage": "https://github.com/spatie/laravel-data", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.1", "illuminate/contracts": "^10.0|^11.0|^12.0", "phpdocumentor/reflection": "^6.0", "spatie/laravel-package-tools": "^1.9.0", "spatie/php-structure-discoverer": "^2.0"}, "require-dev": {"fakerphp/faker": "^1.14", "friendsofphp/php-cs-fixer": "^3.0", "inertiajs/inertia-laravel": "^2.0", "livewire/livewire": "^3.0", "mockery/mockery": "^1.6", "nesbot/carbon": "^2.63|^3.0", "orchestra/testbench": "^8.0|^9.0|^10.0", "pestphp/pest": "^2.31|^3.0", "pestphp/pest-plugin-laravel": "^2.0|^3.0", "pestphp/pest-plugin-livewire": "^2.1|^3.0", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpunit/phpunit": "^10.0|^11.0|^12.0", "spatie/invade": "^1.0", "spatie/laravel-typescript-transformer": "^2.5", "spatie/pest-plugin-snapshots": "^2.1", "spatie/test-time": "^1.2"}, "autoload": {"psr-4": {"Spatie\\LaravelData\\": "src/"}}, "autoload-dev": {"psr-4": {"Spatie\\LaravelData\\Tests\\": "tests/"}}, "scripts": {"analyse": "vendor/bin/phpstan analyse", "test": "./vendor/bin/pest --no-coverage", "test-coverage": "vendor/bin/pest --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes", "benchmark": "vendor/bin/phpbench run --filter=DataBench", "profile": "vendor/bin/phpbench xdebug:profile --filter=DataProfileBench"}, "config": {"sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true, "pestphp/pest-plugin": true}}, "extra": {"laravel": {"providers": ["Spatie\\LaravelData\\LaravelDataServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true}